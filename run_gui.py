#!/usr/bin/env python3
"""
Resume Parser GUI Launcher
Simple script to launch the resume parser with a graphical interface.
"""

import sys
import os

# Change to the script directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Add the resume_parser directory to the path
sys.path.insert(0, os.path.join(script_dir, 'resume_parser'))

try:
    import tkinter as tk
    from tkinter import ttk, scrolledtext, messagebox, filedialog
    print("Tkinter imported successfully")
except ImportError as e:
    print(f"Error importing tkinter: {e}")
    print("Please make sure you have tkinter installed")
    sys.exit(1)

try:
    from resume_parser.gui_app import main
    print("Starting GUI...")
    main()
except ImportError as e:
    print(f"Import error: {e}")
    print("Trying alternative import method...")

    # Alternative import method
    sys.path.insert(0, os.path.join(script_dir))
    try:
        from resume_parser.gui_app import main
        main()
    except Exception as e2:
        print(f"Failed to start GUI: {e2}")
        print("Please run: python3 -m resume_parser.gui_app")
