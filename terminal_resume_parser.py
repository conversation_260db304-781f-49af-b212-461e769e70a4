#!/usr/bin/env python3
"""
Terminal Resume Parser
Command-line interface that definitely works on macOS.
"""

import os
import sys
import subprocess
import re

print("🚀 Terminal Resume Parser - Always Works!")
print("=" * 50)

def install_package(package):
    try:
        __import__(package)
        return True
    except ImportError:
        print(f"📦 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            return True
        except:
            return False

# Load packages
PyPDF2 = None
docx = None

try:
    if install_package("PyPDF2"):
        import PyPDF2
    if install_package("python-docx"):
        import docx
    print("✅ Packages loaded!")
except:
    print("⚠️ Using basic text processing")

class TerminalResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "filename": os.path.basename(self.file_path)
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf') and PyPDF2:
            try:
                with open(self.file_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    text = ""
                    for page in reader.pages:
                        text += page.extract_text() + "\n"
                    return text
            except Exception as e:
                return f"Error reading PDF: {e}"
        elif self.file_path.endswith('.docx') and docx:
            try:
                doc = docx.Document(self.file_path)
                text = ""
                for para in doc.paragraphs:
                    text += para.text + "\n"
                return text
            except Exception as e:
                return f"Error reading DOCX: {e}"
        return f"Resume file: {os.path.basename(self.file_path)}"

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5 and len(line) > 3:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "No email found"

    def _extract_phone(self, text):
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "No phone found"

    def _extract_skills(self, text):
        skills = [
            "python", "java", "javascript", "html", "css", "sql", "react", "angular", "vue",
            "aws", "azure", "gcp", "docker", "kubernetes", "git", "machine learning", 
            "data analysis", "data science", "tensorflow", "pytorch", "pandas",
            "excel", "powerpoint", "project management", "agile", "scrum", "leadership"
        ]
        
        found = []
        text_lower = text.lower()
        for skill in skills:
            if re.search(r'\b' + re.escape(skill) + r'\b', text_lower):
                found.append(skill)
        
        return found if found else ["General skills"]

    def _extract_education(self, text):
        degrees = ["bachelor", "master", "phd", "doctorate", "college", "university"]
        found = []
        text_lower = text.lower()
        for degree in degrees:
            if degree in text_lower:
                found.append(degree.title())
        return found if found else ["Education background"]

    def _extract_experience(self, text):
        # Look for years of experience
        years_patterns = [
            r'(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience|exp)',
            r'(\d{4})\s*[-–—to]\s*(\d{4}|\bpresent\b)',
        ]
        
        max_years = 0
        for pattern in years_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches:
                if isinstance(match, tuple):
                    if match[1].isdigit():
                        years = int(match[1]) - int(match[0])
                    else:
                        years = 2024 - int(match[0])
                else:
                    years = int(match)
                max_years = max(max_years, years)
        
        return max_years

class TerminalJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = self._calculate_score(resume)
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)
    
    def _calculate_score(self, resume):
        score = 0
        
        # Skills matching (70%)
        required_skills = set(s.lower().strip() for s in self.requirements.get("required_skills", []))
        candidate_skills = set(s.lower() for s in resume["skills"])
        
        if required_skills:
            matches = required_skills.intersection(candidate_skills)
            skill_score = len(matches) / len(required_skills)
            score += skill_score * 70
        
        # Education (15%)
        if resume["education"]:
            score += 15
        
        # Experience (15%)
        if resume["experience"] >= self.requirements.get("experience", 0):
            score += 15
        
        return min(score, 100)

def get_folder_path():
    """Get resume folder path from user"""
    print("\n📁 Resume Folder Selection:")
    print("1. Use default 'resumes/' folder")
    print("2. Enter custom folder path")
    
    choice = input("\nEnter choice (1 or 2): ").strip()
    
    if choice == "2":
        folder = input("Enter folder path: ").strip()
        if folder.startswith("~"):
            folder = os.path.expanduser(folder)
    else:
        folder = "resumes/"
    
    return folder

def check_files(folder):
    """Check and display resume files"""
    if not os.path.exists(folder):
        print(f"❌ Folder '{folder}' does not exist!")
        return []
    
    files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
    
    if files:
        print(f"\n✅ Found {len(files)} resume files:")
        for i, filename in enumerate(files, 1):
            file_path = os.path.join(folder, filename)
            size = os.path.getsize(file_path) / 1024  # KB
            print(f"   {i}. {filename} ({size:.1f} KB)")
    else:
        print(f"❌ No PDF or DOCX files found in '{folder}'")
    
    return files

def get_job_requirements():
    """Get job requirements from user"""
    print("\n⚙️ Job Requirements:")
    
    # Skills
    print("\nRequired Skills (comma-separated):")
    print("Examples: python, sql, react, aws, machine learning")
    skills_input = input("Enter skills: ").strip()
    skills = [s.strip() for s in skills_input.split(',') if s.strip()] if skills_input else ["python"]
    
    # Experience
    experience_input = input("\nMinimum experience (years, default 0): ").strip()
    try:
        experience = int(experience_input) if experience_input else 0
    except ValueError:
        experience = 0
    
    return {
        "required_skills": skills,
        "experience": experience
    }

def analyze_resumes(folder, files, requirements):
    """Analyze all resumes"""
    print(f"\n🔍 Analyzing {len(files)} resumes...")
    print("=" * 50)
    
    resumes = []
    
    for i, filename in enumerate(files, 1):
        print(f"📄 Processing {i}/{len(files)}: {filename}")
        
        try:
            file_path = os.path.join(folder, filename)
            parser = TerminalResumeParser(file_path)
            resume_data = parser.parse()
            resumes.append(resume_data)
            print(f"   ✅ Processed successfully")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    if not resumes:
        print("\n❌ No resumes were processed successfully!")
        return
    
    print(f"\n✅ Successfully processed {len(resumes)} resumes")
    
    # Rank resumes
    matcher = TerminalJobMatcher(requirements)
    ranked = matcher.rank_resumes(resumes)
    
    # Display results
    print(f"\n🏆 RANKING RESULTS:")
    print("=" * 70)
    print(f"Looking for: {', '.join(requirements['required_skills'])}")
    print(f"Min experience: {requirements['experience']} years")
    print("=" * 70)
    
    for i, (resume, score) in enumerate(ranked, 1):
        # Score indicator
        if score >= 80:
            indicator = "🥇 EXCELLENT"
        elif score >= 60:
            indicator = "🥈 GOOD"
        elif score >= 40:
            indicator = "🥉 FAIR"
        else:
            indicator = "📋 BASIC"
        
        print(f"\n{indicator} {i}. {resume['name']}")
        print(f"   📊 Match Score: {score:.1f}/100")
        print(f"   📧 Email: {resume['email']}")
        print(f"   📞 Phone: {resume['phone']}")
        
        # Skills
        skills_display = ', '.join(resume['skills'][:6])
        if len(resume['skills']) > 6:
            skills_display += f" ... and {len(resume['skills'])-6} more"
        print(f"   🛠️  Skills: {skills_display}")
        
        # Education
        education_display = ', '.join(resume['education'][:3])
        if len(resume['education']) > 3:
            education_display += " ..."
        print(f"   🎓 Education: {education_display}")
        
        print(f"   💼 Experience: {resume['experience']} years")
        print(f"   📁 File: {resume['filename']}")
        print("   " + "-" * 60)

def main():
    """Main terminal interface"""
    print("Welcome to Terminal Resume Parser!")
    print("This interface always works, even when GUIs don't.")
    
    try:
        # Get folder path
        folder = get_folder_path()
        
        # Check files
        files = check_files(folder)
        if not files:
            return
        
        # Get requirements
        requirements = get_job_requirements()
        
        # Confirm and analyze
        print(f"\n📋 Summary:")
        print(f"   Folder: {folder}")
        print(f"   Files: {len(files)} resumes")
        print(f"   Skills: {', '.join(requirements['required_skills'])}")
        print(f"   Experience: {requirements['experience']} years")
        
        confirm = input("\nProceed with analysis? (y/n): ").strip().lower()
        if confirm in ['y', 'yes', '']:
            analyze_resumes(folder, files, requirements)
        else:
            print("Analysis cancelled.")
    
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
