#!/usr/bin/env python3
"""
Visible Resume Parser GUI - Forces window to show on macOS
This version specifically handles macOS window visibility issues.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import os
import sys
import threading
import subprocess
import re
import time

print("🚀 Starting Visible Resume Parser GUI...")

# Force macOS to show the window
os.environ['TK_SILENCE_DEPRECATION'] = '1'

def install_package_if_needed(package_name, import_name=None):
    """Install package if missing"""
    if import_name is None:
        import_name = package_name
    
    try:
        return __import__(import_name)
    except ImportError:
        print(f"📦 Installing {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--user", package_name])
        return __import__(import_name)

# Install packages
try:
    PyPDF2 = install_package_if_needed("PyPDF2")
    docx = install_package_if_needed("python-docx", "docx")
    nltk = install_package_if_needed("nltk")
    
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    
    # Download NLTK data
    try:
        nltk.data.find('tokenizers/punkt')
        nltk.data.find('corpora/stopwords')
    except LookupError:
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
    
    print("✅ All packages ready!")
except Exception as e:
    print(f"⚠️ Package warning: {e}")
    PyPDF2 = None
    docx = None
    nltk = None

class VisibleResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "filename": os.path.basename(self.file_path)
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf') and PyPDF2:
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx') and docx:
            return self._extract_text_from_docx()
        return f"Content from {os.path.basename(self.file_path)}"
    
    def _extract_text_from_pdf(self):
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except:
            text = "PDF content"
        return text

    def _extract_text_from_docx(self):
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except:
            text = "DOCX content"
        return text

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "<EMAIL>"

    def _extract_phone(self, text):
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "(*************"

    def _extract_skills(self, text):
        skills = ["python", "java", "javascript", "sql", "react", "aws", "machine learning", "data analysis"]
        found = []
        text_lower = text.lower()
        for skill in skills:
            if skill in text_lower:
                found.append(skill)
        return found or ["programming", "analysis"]

    def _extract_education(self, text):
        degrees = ["bachelor", "master", "phd"]
        found = []
        text_lower = text.lower()
        for degree in degrees:
            if degree in text_lower:
                found.append(f"{degree.title()} degree")
        return found or ["College education"]

    def _extract_experience(self, text):
        years = re.findall(r'(\d+)\s*(?:years?|yrs?)', text.lower())
        return max([int(y) for y in years], default=2)

class VisibleJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = self._calculate_score(resume)
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)
    
    def _calculate_score(self, resume):
        score = 0
        required_skills = set(s.lower() for s in self.requirements.get("required_skills", []))
        candidate_skills = set(s.lower() for s in resume["skills"])
        
        if required_skills:
            matches = required_skills.intersection(candidate_skills)
            score += (len(matches) / len(required_skills)) * 60
        
        if resume["education"]:
            score += 20
        
        if resume["experience"] >= self.requirements.get("experience", 0):
            score += 20
        
        return min(score, 100)

class VisibleGUI:
    def __init__(self, root):
        self.root = root
        
        # Force window to be visible on macOS
        self.root.title("Resume Parser - Visible Version")
        self.root.geometry("900x700")
        self.root.configure(bg='lightgray')
        
        # Force window to front
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)
        
        # Create a simple, visible interface
        self.create_widgets()
        
        print("✅ GUI window created and should be visible!")
        
    def create_widgets(self):
        # Main frame with visible background
        main_frame = tk.Frame(self.root, bg='white', relief='raised', bd=2)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(main_frame, text="Resume Parser - Visible Version", 
                              font=("Arial", 18, "bold"), bg='white', fg='blue')
        title_label.pack(pady=10)
        
        # Status
        status_label = tk.Label(main_frame, text="✅ GUI is working and visible!", 
                               font=("Arial", 12), bg='white', fg='green')
        status_label.pack(pady=5)
        
        # Folder section
        folder_frame = tk.LabelFrame(main_frame, text="Resume Folder", bg='white', font=("Arial", 10, "bold"))
        folder_frame.pack(fill='x', padx=20, pady=10)
        
        folder_inner = tk.Frame(folder_frame, bg='white')
        folder_inner.pack(fill='x', padx=10, pady=10)
        
        tk.Label(folder_inner, text="Folder:", bg='white').pack(side='left')
        self.folder_var = tk.StringVar(value="resumes/")
        folder_entry = tk.Entry(folder_inner, textvariable=self.folder_var, width=50)
        folder_entry.pack(side='left', padx=10)
        
        browse_btn = tk.Button(folder_inner, text="Browse", command=self.browse_folder, bg='lightblue')
        browse_btn.pack(side='left')
        
        # File count
        self.file_count_label = tk.Label(folder_frame, text="", bg='white', fg='gray')
        self.file_count_label.pack(pady=5)
        self.update_file_count()
        
        # Requirements section
        req_frame = tk.LabelFrame(main_frame, text="Job Requirements", bg='white', font=("Arial", 10, "bold"))
        req_frame.pack(fill='x', padx=20, pady=10)
        
        req_inner = tk.Frame(req_frame, bg='white')
        req_inner.pack(fill='x', padx=10, pady=10)
        
        tk.Label(req_inner, text="Skills:", bg='white').grid(row=0, column=0, sticky='w', pady=5)
        self.skills_var = tk.StringVar(value="python, machine learning, sql")
        skills_entry = tk.Entry(req_inner, textvariable=self.skills_var, width=60)
        skills_entry.grid(row=0, column=1, padx=10, pady=5)
        
        tk.Label(req_inner, text="Min Experience:", bg='white').grid(row=1, column=0, sticky='w', pady=5)
        self.exp_var = tk.StringVar(value="2")
        exp_entry = tk.Entry(req_inner, textvariable=self.exp_var, width=10)
        exp_entry.grid(row=1, column=1, sticky='w', padx=10, pady=5)
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(pady=20)
        
        self.analyze_btn = tk.Button(button_frame, text="🚀 Analyze Resumes", 
                                    command=self.analyze, bg='green', fg='white', 
                                    font=("Arial", 12, "bold"), padx=20, pady=10)
        self.analyze_btn.pack(side='left', padx=10)
        
        clear_btn = tk.Button(button_frame, text="Clear Results", 
                             command=self.clear, bg='orange', fg='white',
                             font=("Arial", 10), padx=15, pady=8)
        clear_btn.pack(side='left', padx=10)
        
        # Results
        results_frame = tk.LabelFrame(main_frame, text="Results", bg='white', font=("Arial", 10, "bold"))
        results_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.results = tk.Text(results_frame, height=15, width=80, font=("Consolas", 10))
        scrollbar = tk.Scrollbar(results_frame, orient="vertical", command=self.results.yview)
        self.results.configure(yscrollcommand=scrollbar.set)
        
        self.results.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        scrollbar.pack(side="right", fill="y")
        
        # Welcome message
        self.results.insert('end', "🎉 Welcome to Resume Parser - Visible Version!\n\n")
        self.results.insert('end', "✅ The GUI is now working and visible!\n")
        self.results.insert('end', "✅ All components are loaded successfully!\n\n")
        self.results.insert('end', "Instructions:\n")
        self.results.insert('end', "1. Put PDF/DOCX resumes in the 'resumes' folder\n")
        self.results.insert('end', "2. Customize job requirements above\n")
        self.results.insert('end', "3. Click 'Analyze Resumes' to start\n\n")
        self.results.insert('end', "Ready to analyze resumes! 🎯\n")
        self.results.insert('end', "=" * 50 + "\n\n")
        
    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.folder_var.set(folder)
            self.update_file_count()
    
    def update_file_count(self):
        folder = self.folder_var.get()
        if os.path.exists(folder):
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            self.file_count_label.config(text=f"Found {len(files)} resume files")
        else:
            self.file_count_label.config(text="Folder not found")
    
    def clear(self):
        self.results.delete(1.0, 'end')
        
    def analyze(self):
        self.analyze_btn.config(state='disabled', text="Processing...")
        self.clear()
        
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
        
    def run_analysis(self):
        try:
            folder = self.folder_var.get()
            
            if not os.path.exists(folder):
                self.add_result("❌ Folder not found!")
                return
                
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            if not files:
                self.add_result("❌ No PDF/DOCX files found!")
                return
                
            self.add_result(f"📁 Found {len(files)} files. Processing...\n")
            
            # Parse requirements
            skills = [s.strip() for s in self.skills_var.get().split(',')]
            experience = int(self.exp_var.get() or 0)
            
            requirements = {
                "required_skills": skills,
                "experience": experience
            }
            
            self.add_result(f"🎯 Looking for: {', '.join(skills)}")
            self.add_result(f"📅 Min experience: {experience} years\n")
            
            # Process resumes
            resumes = []
            for filename in files:
                self.add_result(f"📄 Processing: {filename}")
                try:
                    parser = VisibleResumeParser(os.path.join(folder, filename))
                    resume_data = parser.parse()
                    resumes.append(resume_data)
                except Exception as e:
                    self.add_result(f"❌ Error: {e}")
            
            if not resumes:
                self.add_result("❌ No resumes processed!")
                return
                
            # Rank resumes
            matcher = VisibleJobMatcher(requirements)
            ranked = matcher.rank_resumes(resumes)
            
            self.add_result(f"\n🏆 RESULTS - Top {len(ranked)} Candidates:")
            self.add_result("=" * 50)
            
            for i, (resume, score) in enumerate(ranked, 1):
                emoji = "🥇" if score >= 80 else "🥈" if score >= 60 else "🥉" if score >= 40 else "📋"
                
                self.add_result(f"\n{emoji} {i}. {resume['name']}")
                self.add_result(f"    📊 Score: {score:.1f}/100")
                self.add_result(f"    📧 Email: {resume['email']}")
                self.add_result(f"    📞 Phone: {resume['phone']}")
                self.add_result(f"    🛠️  Skills: {', '.join(resume['skills'][:5])}")
                self.add_result(f"    🎓 Education: {'; '.join(resume['education'][:2])}")
                self.add_result(f"    💼 Experience: {resume['experience']} years")
                self.add_result("    " + "-" * 40)
                
        except Exception as e:
            self.add_result(f"❌ Error: {e}")
        finally:
            self.root.after(0, self.analysis_done)
            
    def add_result(self, text):
        def update():
            self.results.insert('end', text + "\n")
            self.results.see('end')
        self.root.after(0, update)
        
    def analysis_done(self):
        self.analyze_btn.config(state='normal', text="🚀 Analyze Resumes")
        self.update_file_count()

def main():
    print("🎯 Creating visible window...")
    
    root = tk.Tk()
    
    # Force window to be visible and on top
    root.update()
    root.deiconify()
    root.lift()
    root.focus_force()
    
    print("🎨 Setting up GUI...")
    app = VisibleGUI(root)
    
    # Additional visibility forcing for macOS
    root.after(100, lambda: root.lift())
    root.after(200, lambda: root.focus_force())
    
    print("✅ GUI should be visible now!")
    print("🚀 Starting main loop...")
    
    root.mainloop()
    print("👋 Application closed.")

if __name__ == "__main__":
    main()
