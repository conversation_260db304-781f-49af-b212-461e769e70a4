#!/usr/bin/env python3
"""
Desktop Resume Parser with File Explorer
A simple desktop GUI with working file browser and resume analysis.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import threading
import subprocess
import re

print("Starting Desktop Resume Parser...")

# Install packages if needed
def install_package(package):
    try:
        __import__(package)
        return True
    except ImportError:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            return True
        except:
            return False

# Load packages
PyPDF2 = None
docx = None

try:
    if install_package("PyPDF2"):
        import PyPDF2
    if install_package("python-docx"):
        import docx
    print("Packages loaded!")
except Exception as e:
    print(f"Package warning: {e}")

class DesktopResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "filename": os.path.basename(self.file_path)
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf') and PyPDF2:
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx') and docx:
            return self._extract_text_from_docx()
        return f"File: {os.path.basename(self.file_path)}"
    
    def _extract_text_from_pdf(self):
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            text = f"Error reading PDF: {e}"
        return text

    def _extract_text_from_docx(self):
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except Exception as e:
            text = f"Error reading DOCX: {e}"
        return text

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "No email found"

    def _extract_phone(self, text):
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "No phone found"

    def _extract_skills(self, text):
        skills = [
            "python", "java", "javascript", "html", "css", "sql", "react", "angular", 
            "aws", "azure", "docker", "git", "machine learning", "data analysis",
            "project management", "excel", "communication", "leadership"
        ]
        
        found = []
        text_lower = text.lower()
        for skill in skills:
            if skill in text_lower:
                found.append(skill)
        
        return found if found else ["General skills"]

    def _extract_education(self, text):
        degrees = ["bachelor", "master", "phd", "college", "university"]
        found = []
        text_lower = text.lower()
        for degree in degrees:
            if degree in text_lower:
                found.append(degree.title())
        return found if found else ["Education"]

    def _extract_experience(self, text):
        years = re.findall(r'(\d+)\s*(?:years?|yrs?)', text.lower())
        return max([int(y) for y in years], default=1)

class DesktopJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = self._calculate_score(resume)
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)
    
    def _calculate_score(self, resume):
        score = 0
        required_skills = set(s.lower().strip() for s in self.requirements.get("required_skills", []))
        candidate_skills = set(s.lower() for s in resume["skills"])
        
        if required_skills:
            matches = required_skills.intersection(candidate_skills)
            score += (len(matches) / len(required_skills)) * 70
        
        if resume["education"]:
            score += 15
        
        if resume["experience"] >= self.requirements.get("experience", 0):
            score += 15
        
        return min(score, 100)

class DesktopGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Resume Parser - Desktop Version")
        self.root.geometry("900x700")
        
        # Variables
        self.folder_var = tk.StringVar(value="resumes/")
        self.skills_var = tk.StringVar(value="python, machine learning, sql")
        self.experience_var = tk.StringVar(value="2")
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Resume Parser - Desktop Version", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Status
        status_label = ttk.Label(main_frame, text="Desktop GUI with working file browser!", 
                                font=("Arial", 10), foreground="green")
        status_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # Folder section
        folder_frame = ttk.LabelFrame(main_frame, text="Resume Folder", padding="15")
        folder_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        folder_frame.columnconfigure(1, weight=1)
        
        ttk.Label(folder_frame, text="Folder:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, width=60)
        folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # Browse button - this will open file explorer!
        browse_btn = ttk.Button(folder_frame, text="Browse Folder", command=self.browse_folder)
        browse_btn.grid(row=0, column=2)
        
        # File count
        self.file_count_label = ttk.Label(folder_frame, text="")
        self.file_count_label.grid(row=1, column=0, columnspan=3, pady=(10, 0))
        
        # Check files button
        check_btn = ttk.Button(folder_frame, text="Check Files", command=self.check_files)
        check_btn.grid(row=2, column=0, pady=(10, 0))
        
        # Requirements section
        req_frame = ttk.LabelFrame(main_frame, text="Job Requirements", padding="15")
        req_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        req_frame.columnconfigure(1, weight=1)
        
        ttk.Label(req_frame, text="Required Skills:").grid(row=0, column=0, sticky=tk.W, pady=5)
        skills_entry = ttk.Entry(req_frame, textvariable=self.skills_var, width=60)
        skills_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        ttk.Label(req_frame, text="Min Experience (years):").grid(row=1, column=0, sticky=tk.W, pady=5)
        exp_entry = ttk.Entry(req_frame, textvariable=self.experience_var, width=10)
        exp_entry.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(0, 15))
        
        self.analyze_btn = ttk.Button(button_frame, text="Analyze Resumes", command=self.analyze_resumes)
        self.analyze_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = ttk.Button(button_frame, text="Clear Results", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT)
        
        # Results
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="15")
        results_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=20, width=80, 
                                                     font=("Consolas", 10))
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        main_frame.rowconfigure(5, weight=1)
        
        # Initial message
        self.results_text.insert(tk.END, "Welcome to Resume Parser Desktop Version!\n\n")
        self.results_text.insert(tk.END, "Features:\n")
        self.results_text.insert(tk.END, "- Working 'Browse Folder' button opens file explorer\n")
        self.results_text.insert(tk.END, "- 'Check Files' shows resume count\n")
        self.results_text.insert(tk.END, "- Full resume analysis and ranking\n\n")
        self.results_text.insert(tk.END, "Instructions:\n")
        self.results_text.insert(tk.END, "1. Click 'Browse Folder' to select resume folder\n")
        self.results_text.insert(tk.END, "2. Click 'Check Files' to see resume count\n")
        self.results_text.insert(tk.END, "3. Customize job requirements\n")
        self.results_text.insert(tk.END, "4. Click 'Analyze Resumes'\n\n")
        self.results_text.insert(tk.END, "Ready to analyze resumes!\n")
        
        # Auto-check files
        self.check_files()
    
    def browse_folder(self):
        """Open file explorer to select folder - this actually works!"""
        folder = filedialog.askdirectory(
            title="Select Resume Folder",
            initialdir=self.folder_var.get() if os.path.exists(self.folder_var.get()) else os.getcwd()
        )
        
        if folder:
            self.folder_var.set(folder)
            self.check_files()
            self.add_result(f"Selected folder: {folder}")
    
    def check_files(self):
        """Check how many resume files are in the folder"""
        folder = self.folder_var.get()
        
        if os.path.exists(folder):
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            count = len(files)
            self.file_count_label.config(text=f"Found {count} resume files (.pdf and .docx)")
            
            if count > 0:
                self.file_count_label.config(foreground="green")
            else:
                self.file_count_label.config(foreground="orange")
        else:
            self.file_count_label.config(text="Folder not found", foreground="red")
    
    def analyze_resumes(self):
        """Analyze all resumes in the folder"""
        self.analyze_btn.config(state='disabled', text="Processing...")
        self.clear_results()
        
        # Run in separate thread to prevent GUI freezing
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
    
    def run_analysis(self):
        try:
            folder = self.folder_var.get()
            
            if not os.path.exists(folder):
                self.add_result("Error: Folder does not exist!")
                return
            
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            if not files:
                self.add_result("Error: No PDF or DOCX files found!")
                return
            
            self.add_result(f"Found {len(files)} resume files. Processing...\n")
            
            # Parse requirements
            skills_text = self.skills_var.get()
            skills = [s.strip() for s in skills_text.split(',') if s.strip()]
            experience = int(self.experience_var.get() or 0)
            
            requirements = {
                "required_skills": skills,
                "experience": experience
            }
            
            self.add_result(f"Looking for skills: {', '.join(skills)}")
            self.add_result(f"Minimum experience: {experience} years\n")
            
            # Process resumes
            resumes = []
            for i, filename in enumerate(files, 1):
                self.add_result(f"Processing {i}/{len(files)}: {filename}")
                try:
                    parser = DesktopResumeParser(os.path.join(folder, filename))
                    resume_data = parser.parse()
                    resumes.append(resume_data)
                except Exception as e:
                    self.add_result(f"  Error: {str(e)}")
            
            if resumes:
                self.add_result(f"\nSuccessfully processed {len(resumes)} resumes")
                
                # Rank resumes
                matcher = DesktopJobMatcher(requirements)
                ranked = matcher.rank_resumes(resumes)
                
                self.add_result(f"\nRANKING RESULTS:")
                self.add_result("=" * 60)
                
                for i, (resume, score) in enumerate(ranked, 1):
                    self.add_result(f"\n{i}. {resume['name']}")
                    self.add_result(f"   Score: {score:.1f}/100")
                    self.add_result(f"   Email: {resume['email']}")
                    self.add_result(f"   Phone: {resume['phone']}")
                    self.add_result(f"   Skills: {', '.join(resume['skills'][:6])}")
                    if len(resume['skills']) > 6:
                        self.add_result(f"           ... and {len(resume['skills'])-6} more")
                    self.add_result(f"   Education: {', '.join(resume['education'][:3])}")
                    self.add_result(f"   Experience: {resume['experience']} years")
                    self.add_result("   " + "-" * 50)
            else:
                self.add_result("\nNo resumes were processed successfully")
                
        except Exception as e:
            self.add_result(f"\nError during analysis: {str(e)}")
        finally:
            # Re-enable button
            self.root.after(0, self.analysis_complete)
    
    def add_result(self, text):
        """Add text to results (thread-safe)"""
        def update():
            self.results_text.insert(tk.END, text + "\n")
            self.results_text.see(tk.END)
        self.root.after(0, update)
    
    def clear_results(self):
        """Clear the results area"""
        self.results_text.delete(1.0, tk.END)
    
    def analysis_complete(self):
        """Re-enable the analyze button"""
        self.analyze_btn.config(state='normal', text="Analyze Resumes")

def main():
    print("Creating desktop GUI...")
    root = tk.Tk()
    app = DesktopGUI(root)
    print("Desktop GUI ready!")
    root.mainloop()

if __name__ == "__main__":
    main()
