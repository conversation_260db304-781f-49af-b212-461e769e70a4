def _extract_text_from_pdf(self):
    """Extract text from PDF files"""
    text = ""
    try:
        with open(self.file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page in reader.pages:
                text += page.extract_text() + "\n"
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
    return text

def _extract_text_from_docx(self):
    """Extract text from DOCX files"""
    text = ""
    try:
        doc = docx.Document(self.file_path)
        for para in doc.paragraphs:
            text += para.text + "\n"
    except Exception as e:
        print(f"Error extracting text from DOCX: {e}")
    return text

def _extract_name(self, text):
    """Extract candidate name from resume text"""
    # Simple heuristic: first line often contains the name
    lines = text.split('\n')
    for line in lines[:5]:  # Check first 5 lines
        line = line.strip()
        if line and len(line.split()) <= 5:  # Names typically have few words
            return line
    return "Unknown"

def _extract_email(self, text):
    """Extract email address from resume text"""
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    return emails[0] if emails else ""

def _extract_phone(self, text):
    """Extract phone number from resume text"""
    phone_pattern = r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}'
    phones = re.findall(phone_pattern, text)
    return ''.join(''.join(tup) for tup in phones[:1]) if phones else ""