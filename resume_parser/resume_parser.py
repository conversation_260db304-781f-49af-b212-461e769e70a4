try:
    import PyPDF2
    import docx
    import re
    import nltk
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages with: pip3 install PyPDF2 python-docx nltk")
    raise

class ResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        # Download NLTK resources if needed
        try:
            nltk.data.find('tokenizers/punkt')
            nltk.data.find('corpora/stopwords')
        except LookupError:
            nltk.download('punkt')
            nltk.download('stopwords')

        self.stop_words = set(stopwords.words('english'))

    def parse(self):
        """Parse resume file and extract relevant information"""
        text = self._extract_text()

        # Extract basic information
        name = self._extract_name(text)
        email = self._extract_email(text)
        phone = self._extract_phone(text)
        skills = self._extract_skills(text)
        education = self._extract_education(text)
        experience = self._extract_experience(text)

        return {
            "name": name,
            "email": email,
            "phone": phone,
            "skills": skills,
            "education": education,
            "experience": experience,
            "raw_text": text
        }

    def _extract_text(self):
        """Extract text from file based on file type"""
        if self.file_path.endswith('.pdf'):
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx'):
            return self._extract_text_from_docx()
        return ""

    def _extract_text_from_pdf(self):
        """Extract text from PDF files"""
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
        return text

    def _extract_text_from_docx(self):
        """Extract text from DOCX files"""
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
        return text

    def _extract_name(self, text):
        """Extract candidate name from resume text"""
        # Simple heuristic: first line often contains the name
        lines = text.split('\n')
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if line and len(line.split()) <= 5:  # Names typically have few words
                return line
        return "Unknown"

    def _extract_email(self, text):
        """Extract email address from resume text"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        return emails[0] if emails else ""

    def _extract_phone(self, text):
        """Extract phone number from resume text"""
        phone_pattern = r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}'
        phones = re.findall(phone_pattern, text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else ""

    def _extract_skills(self, text):
        """Extract skills from resume text"""
        # Common tech skills to look for
        common_skills = [
            "python", "java", "javascript", "html", "css", "sql", "nosql", "mongodb",
            "react", "angular", "vue", "node.js", "express", "django", "flask",
            "aws", "azure", "gcp", "docker", "kubernetes", "ci/cd", "git",
            "machine learning", "data analysis", "data science", "artificial intelligence",
            "nlp", "computer vision", "deep learning", "tensorflow", "pytorch",
            "agile", "scrum", "project management", "leadership"
        ]

        skills = []
        text_lower = text.lower()

        # Find common skills in text
        for skill in common_skills:
            if re.search(r'\b' + re.escape(skill) + r'\b', text_lower):
                skills.append(skill)

        # Extract additional skills from "skills" or "technical skills" sections
        skills_section = self._extract_section(text_lower, ["skills", "technical skills"])
        if skills_section:
            # Tokenize and filter out stop words
            tokens = word_tokenize(skills_section)
            potential_skills = [token.lower() for token in tokens if token.lower() not in self.stop_words
                               and token.isalpha() and len(token) > 2]

            # Add unique skills
            for skill in potential_skills:
                if skill not in skills:
                    skills.append(skill)

        return skills

    def _extract_section(self, text, section_headers):
        """Extract a specific section from resume text"""
        for header in section_headers:
            pattern = re.compile(r'(?i)' + re.escape(header) + r'[:\s]*(.*?)(?:\n\s*\n|\Z)', re.DOTALL)
            match = pattern.search(text)
            if match:
                return match.group(1).strip()
        return ""

    def _extract_education(self, text):
        """Extract education information from resume text"""
        education = []
        education_section = self._extract_section(text.lower(), ["education", "academic background"])

        if education_section:
            # Look for degree keywords
            degree_keywords = ["bachelor", "master", "phd", "doctorate", "bs", "ms", "ba", "ma", "mba"]
            for keyword in degree_keywords:
                if keyword in education_section:
                    # Find the sentence containing the degree
                    sentences = re.split(r'[.!?]+', education_section)
                    for sentence in sentences:
                        if keyword in sentence.lower():
                            education.append(sentence.strip())

        return education

    def _extract_experience(self, text):
        """Extract work experience information and estimate years"""
        experience_section = self._extract_section(text.lower(), ["experience", "work experience", "employment"])

        if not experience_section:
            return 0

        # Look for year patterns (e.g., 2018-2022, 2018 - present)
        year_patterns = [
            r'(\d{4})\s*[-–—to]\s*(\d{4}|\bpresent\b)',
            r'(\d{4})\s*[-–—]\s*(\d{4}|\bpresent\b)',
            r'(\bsince\b)\s*(\d{4})'
        ]

        total_years = 0
        current_year = 2023  # Use current year as default

        for pattern in year_patterns:
            matches = re.findall(pattern, experience_section, re.IGNORECASE)
            for match in matches:
                start_year = 0
                end_year = 0

                if match[0].isdigit():
                    start_year = int(match[0])
                    if match[1].isdigit():
                        end_year = int(match[1])
                    elif "present" in match[1].lower():
                        end_year = current_year
                elif "since" in match[0].lower() and match[1].isdigit():
                    start_year = int(match[1])
                    end_year = current_year

                if start_year and end_year:
                    total_years += (end_year - start_year)

        return total_years