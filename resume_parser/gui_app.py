import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import os
import sys
import threading

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from resume_parser import ResumeParser
from job_matcher import JobMatcher

class ResumeParserGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Resume Parser & Analyzer")
        self.root.geometry("800x700")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Resume Parser & Analyzer", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Resume folder section
        folder_frame = ttk.LabelFrame(main_frame, text="Resume Files", padding="10")
        folder_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        folder_frame.columnconfigure(1, weight=1)
        
        ttk.Label(folder_frame, text="Resumes Folder:").grid(row=0, column=0, sticky=tk.W)
        self.folder_path = tk.StringVar(value="resumes/")
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_path, width=50)
        folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        
        browse_btn = ttk.Button(folder_frame, text="Browse", command=self.browse_folder)
        browse_btn.grid(row=0, column=2, padx=(5, 0))
        
        # File count label
        self.file_count_label = ttk.Label(folder_frame, text="")
        self.file_count_label.grid(row=1, column=0, columnspan=3, pady=(5, 0))
        self.update_file_count()
        
        # Job requirements section
        req_frame = ttk.LabelFrame(main_frame, text="Job Requirements", padding="10")
        req_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        req_frame.columnconfigure(1, weight=1)
        
        # Skills
        ttk.Label(req_frame, text="Required Skills:").grid(row=0, column=0, sticky=tk.W)
        self.skills_var = tk.StringVar(value="python, machine learning, data analysis, sql")
        skills_entry = ttk.Entry(req_frame, textvariable=self.skills_var, width=60)
        skills_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # Education
        ttk.Label(req_frame, text="Education:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.education_var = tk.StringVar(value="bachelor, computer science")
        education_entry = ttk.Entry(req_frame, textvariable=self.education_var, width=60)
        education_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(5, 0))
        
        # Experience
        ttk.Label(req_frame, text="Min Experience (years):").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.experience_var = tk.StringVar(value="2")
        experience_entry = ttk.Entry(req_frame, textvariable=self.experience_var, width=10)
        experience_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(0, 10))
        
        # Analyze button
        self.analyze_btn = ttk.Button(button_frame, text="Analyze Resumes", 
                                     command=self.analyze_resumes, style="Accent.TButton")
        self.analyze_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Clear button
        clear_btn = ttk.Button(button_frame, text="Clear Results", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Results text area with scrollbar
        self.results_text = scrolledtext.ScrolledText(results_frame, height=20, width=80)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure main frame row weights
        main_frame.rowconfigure(5, weight=1)
        
    def browse_folder(self):
        folder = filedialog.askdirectory(title="Select Resume Folder")
        if folder:
            self.folder_path.set(folder)
            self.update_file_count()
    
    def update_file_count(self):
        folder = self.folder_path.get()
        if os.path.exists(folder):
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            count = len(files)
            self.file_count_label.config(text=f"Found {count} resume files")
        else:
            self.file_count_label.config(text="Folder not found")
    
    def clear_results(self):
        self.results_text.delete(1.0, tk.END)
    
    def analyze_resumes(self):
        # Validate inputs
        folder = self.folder_path.get()
        if not os.path.exists(folder):
            messagebox.showerror("Error", "Resume folder does not exist!")
            return
        
        try:
            experience_years = int(self.experience_var.get())
        except ValueError:
            messagebox.showerror("Error", "Experience must be a valid number!")
            return
        
        # Disable button and start progress
        self.analyze_btn.config(state='disabled')
        self.progress.start()
        
        # Run analysis in separate thread to prevent GUI freezing
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
    
    def run_analysis(self):
        try:
            folder = self.folder_path.get()
            
            # Parse job requirements
            skills_text = self.skills_var.get()
            education_text = self.education_var.get()
            experience_years = int(self.experience_var.get())
            
            skills_list = [skill.strip() for skill in skills_text.split(',') if skill.strip()]
            education_list = [edu.strip() for edu in education_text.split(',') if edu.strip()]
            
            job_requirements = {
                "required_skills": skills_list,
                "education": education_list,
                "experience": experience_years
            }
            
            # Find and process resumes
            resume_files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            
            if not resume_files:
                self.update_results("No PDF or DOCX files found in the selected folder.")
                return
            
            parsed_resumes = []
            total_files = len(resume_files)
            
            self.update_results(f"Found {total_files} resume files. Processing...\n")
            
            for i, filename in enumerate(resume_files):
                file_path = os.path.join(folder, filename)
                self.update_results(f"Processing: {filename}")
                
                try:
                    parser = ResumeParser(file_path)
                    resume_data = parser.parse()
                    resume_data['filename'] = filename
                    parsed_resumes.append(resume_data)
                except Exception as e:
                    self.update_results(f"Error processing {filename}: {str(e)}")
            
            if not parsed_resumes:
                self.update_results("\nNo resumes were successfully processed.")
                return
            
            # Display job requirements
            self.update_results(f"\nJob Requirements:")
            self.update_results(f"Skills: {', '.join(skills_list)}")
            self.update_results(f"Education: {', '.join(education_list)}")
            self.update_results(f"Experience: {experience_years} years")
            self.update_results("-" * 60)
            
            # Match and rank resumes
            matcher = JobMatcher(job_requirements)
            ranked_resumes = matcher.rank_resumes(parsed_resumes)
            
            # Display results
            self.update_results(f"\nTop matching resumes (out of {len(parsed_resumes)} processed):")
            self.update_results("=" * 70)
            
            for i, (resume, score) in enumerate(ranked_resumes, 1):
                self.update_results(f"\n{i}. {resume['name']} ({resume['filename']})")
                self.update_results(f"   Match Score: {score:.1f}/100")
                self.update_results(f"   Email: {resume['email']}")
                self.update_results(f"   Phone: {resume['phone']}")
                
                # Show skills (limit to first 8)
                skills_display = ', '.join(resume['skills'][:8])
                if len(resume['skills']) > 8:
                    skills_display += f" ... and {len(resume['skills']) - 8} more"
                self.update_results(f"   Skills: {skills_display}")
                
                # Show education (limit to first 2)
                education_display = '; '.join(resume['education'][:2])
                if len(resume['education']) > 2:
                    education_display += " ..."
                self.update_results(f"   Education: {education_display}")
                
                self.update_results(f"   Experience: {resume['experience']} years")
                self.update_results("-" * 70)
            
        except Exception as e:
            self.update_results(f"\nError during analysis: {str(e)}")
        finally:
            # Re-enable button and stop progress
            self.root.after(0, self.analysis_complete)
    
    def update_results(self, text):
        # Thread-safe way to update GUI
        self.root.after(0, lambda: self._append_text(text))
    
    def _append_text(self, text):
        self.results_text.insert(tk.END, text + "\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()
    
    def analysis_complete(self):
        self.progress.stop()
        self.analyze_btn.config(state='normal')
        self.update_file_count()

def main():
    root = tk.Tk()
    app = ResumeParserGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
