import os
from resume_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from job_matcher import <PERSON><PERSON><PERSON><PERSON>

def main():
    # Parse resumes from a directory
    resume_dir = "resumes/"
    parsed_resumes = []
    
    # Create resumes directory if it doesn't exist
    if not os.path.exists(resume_dir):
        os.makedirs(resume_dir)
        print(f"Created {resume_dir} directory. Please add your PDF and DOCX resumes there.")
        return
    
    # Check if directory has any resume files
    resume_files = [f for f in os.listdir(resume_dir) if f.endswith(('.pdf', '.docx'))]
    if not resume_files:
        print(f"No PDF or DOCX files found in {resume_dir}. Please add some resumes.")
        return
    
    print(f"Found {len(resume_files)} resume files. Processing...")
    
    for filename in resume_files:
        file_path = os.path.join(resume_dir, filename)
        print(f"Processing: {filename}")
        try:
            parser = ResumeParser(file_path)
            resume_data = parser.parse()
            resume_data['filename'] = filename
            parsed_resumes.append(resume_data)
        except Exception as e:
            print(f"Error processing {filename}: {e}")
    
    if not parsed_resumes:
        print("No resumes were successfully processed.")
        return
    
    # Load job requirements - customize these based on your needs
    job_requirements = {
        "required_skills": ["python", "machine learning", "data analysis", "sql"],
        "education": ["bachelor", "computer science"],
        "experience": 2  # years
    }
    
    print(f"\nJob Requirements:")
    print(f"Skills: {', '.join(job_requirements['required_skills'])}")
    print(f"Education: {', '.join(job_requirements['education'])}")
    print(f"Experience: {job_requirements['experience']} years")
    print("-" * 50)
    
    # Match resumes against requirements
    matcher = JobMatcher(job_requirements)
    ranked_resumes = matcher.rank_resumes(parsed_resumes)
    
    # Display results
    print(f"\nTop matching resumes (out of {len(parsed_resumes)} processed):")
    print("=" * 60)
    
    for i, (resume, score) in enumerate(ranked_resumes, 1):
        print(f"{i}. {resume['name']} ({resume['filename']})")
        print(f"   Match Score: {score:.1f}/100")
        print(f"   Email: {resume['email']}")
        print(f"   Phone: {resume['phone']}")
        print(f"   Skills: {', '.join(resume['skills'][:8])}")  # Show first 8 skills
        if len(resume['skills']) > 8:
            print(f"           ... and {len(resume['skills']) - 8} more")
        print(f"   Education: {'; '.join(resume['education'][:2])}")  # Show first 2 education entries
        print(f"   Experience: {resume['experience']} years")
        print("-" * 60)

if __name__ == "__main__":
    main()
