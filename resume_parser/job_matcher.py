class JobMatcher:
    def __init__(self, job_requirements):
        self.requirements = job_requirements
        
    def rank_resumes(self, resumes):
        """Rank resumes based on how well they match job requirements"""
        scored_resumes = []
        
        for resume in resumes:
            score = self._calculate_match_score(resume)
            scored_resumes.append((resume, score))
        
        # Sort by score in descending order
        return sorted(scored_resumes, key=lambda x: x[1], reverse=True)
    
    def _calculate_match_score(self, resume):
        """Calculate a match score between resume and job requirements"""
        score = 0
        
        # Skills matching (50% of total score)
        required_skills = set(skill.lower() for skill in self.requirements["required_skills"])
        candidate_skills = set(skill.lower() for skill in resume["skills"])
        
        skill_matches = required_skills.intersection(candidate_skills)
        skill_score = len(skill_matches) / max(1, len(required_skills))
        score += skill_score * 50
        
        # Education matching (25% of total score)
        education_score = self._match_education(resume["education"])
        score += education_score * 25
        
        # Experience matching (25% of total score)
        experience_score = self._match_experience(resume["experience"])
        score += experience_score * 25
        
        return score
    
    def _match_education(self, education_list):
        """Match education requirements"""
        if not education_list:
            return 0
        
        required_education = [edu.lower() for edu in self.requirements.get("education", [])]
        if not required_education:
            return 1  # No specific education requirements
        
        education_text = " ".join(education_list).lower()
        matches = 0
        
        for req in required_education:
            if req in education_text:
                matches += 1
        
        return matches / len(required_education) if required_education else 0

    def _match_experience(self, years_experience):
        """Match experience requirements"""
        required_years = self.requirements.get("experience", 0)
        
        if required_years == 0:
            return 1  # No experience required
        
        if years_experience >= required_years:
            return 1
        elif years_experience >= required_years * 0.7:
            return 0.7  # Close to required experience
        elif years_experience > 0:
            return 0.3  # Some experience but not enough
        else:
            return 0  # No experience
