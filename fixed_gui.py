#!/usr/bin/env python3
"""
Fixed Resume Parser GUI - No Import Issues
A user-friendly graphical interface for the resume parser with all dependencies handled properly.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import os
import sys
import threading
import re
import subprocess

# Add user site-packages to path to find installed packages
def setup_python_path():
    try:
        import site
        user_site = site.getusersitepackages()
        if user_site not in sys.path:
            sys.path.insert(0, user_site)
    except Exception:
        pass

setup_python_path()

# Function to install missing packages
def install_package(package_name, import_name=None):
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "--user", package_name])
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package_name}")
            return False

# Install required packages if missing
required_packages = [
    ("PyPDF2", "PyPDF2"),
    ("python-docx", "docx"),
    ("nltk", "nltk")
]

print("🔍 Checking required packages...")
for package_name, import_name in required_packages:
    if not install_package(package_name, import_name):
        print(f"❌ Could not install {package_name}")
        sys.exit(1)

# Now import the packages
try:
    import PyPDF2
    import docx
    import nltk
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    print("✅ All packages imported successfully!")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

class FixedResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        # Download NLTK resources if needed
        try:
            nltk.data.find('tokenizers/punkt')
            nltk.data.find('corpora/stopwords')
        except LookupError:
            print("📥 Downloading NLTK data...")
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
        
        self.stop_words = set(stopwords.words('english'))
        
    def parse(self):
        """Parse resume file and extract relevant information"""
        text = self._extract_text()
        
        # Extract basic information
        name = self._extract_name(text)
        email = self._extract_email(text)
        phone = self._extract_phone(text)
        skills = self._extract_skills(text)
        education = self._extract_education(text)
        experience = self._extract_experience(text)
        
        return {
            "name": name,
            "email": email,
            "phone": phone,
            "skills": skills,
            "education": education,
            "experience": experience,
            "raw_text": text
        }
    
    def _extract_text(self):
        """Extract text from file based on file type"""
        if self.file_path.endswith('.pdf'):
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx'):
            return self._extract_text_from_docx()
        return ""
    
    def _extract_text_from_pdf(self):
        """Extract text from PDF files"""
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
        return text

    def _extract_text_from_docx(self):
        """Extract text from DOCX files"""
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
        return text

    def _extract_name(self, text):
        """Extract candidate name from resume text"""
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5:
                return line
        return "Unknown"

    def _extract_email(self, text):
        """Extract email address from resume text"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        return emails[0] if emails else ""

    def _extract_phone(self, text):
        """Extract phone number from resume text"""
        phone_pattern = r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}'
        phones = re.findall(phone_pattern, text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else ""

    def _extract_skills(self, text):
        """Extract skills from resume text"""
        common_skills = [
            "python", "java", "javascript", "html", "css", "sql", "nosql", "mongodb",
            "react", "angular", "vue", "node.js", "express", "django", "flask",
            "aws", "azure", "gcp", "docker", "kubernetes", "ci/cd", "git",
            "machine learning", "data analysis", "data science", "artificial intelligence",
            "nlp", "computer vision", "deep learning", "tensorflow", "pytorch",
            "agile", "scrum", "project management", "leadership"
        ]
        
        skills = []
        text_lower = text.lower()
        
        for skill in common_skills:
            if re.search(r'\b' + re.escape(skill) + r'\b', text_lower):
                skills.append(skill)
        
        skills_section = self._extract_section(text_lower, ["skills", "technical skills"])
        if skills_section:
            tokens = word_tokenize(skills_section)
            potential_skills = [token.lower() for token in tokens if token.lower() not in self.stop_words 
                               and token.isalpha() and len(token) > 2]
            
            for skill in potential_skills:
                if skill not in skills:
                    skills.append(skill)
        
        return skills

    def _extract_section(self, text, section_headers):
        """Extract a specific section from resume text"""
        for header in section_headers:
            pattern = re.compile(r'(?i)' + re.escape(header) + r'[:\s]*(.*?)(?:\n\s*\n|\Z)', re.DOTALL)
            match = pattern.search(text)
            if match:
                return match.group(1).strip()
        return ""

    def _extract_education(self, text):
        """Extract education information from resume text"""
        education = []
        education_section = self._extract_section(text.lower(), ["education", "academic background"])
        
        if education_section:
            degree_keywords = ["bachelor", "master", "phd", "doctorate", "bs", "ms", "ba", "ma", "mba"]
            for keyword in degree_keywords:
                if keyword in education_section:
                    sentences = re.split(r'[.!?]+', education_section)
                    for sentence in sentences:
                        if keyword in sentence.lower():
                            education.append(sentence.strip())
        
        return education

    def _extract_experience(self, text):
        """Extract work experience information and estimate years"""
        experience_section = self._extract_section(text.lower(), ["experience", "work experience", "employment"])
        
        if not experience_section:
            return 0
        
        year_patterns = [
            r'(\d{4})\s*[-–—to]\s*(\d{4}|\bpresent\b)',
            r'(\d{4})\s*[-–—]\s*(\d{4}|\bpresent\b)',
            r'(\bsince\b)\s*(\d{4})'
        ]
        
        total_years = 0
        current_year = 2024
        
        for pattern in year_patterns:
            matches = re.findall(pattern, experience_section, re.IGNORECASE)
            for match in matches:
                start_year = 0
                end_year = 0
                
                if match[0].isdigit():
                    start_year = int(match[0])
                    if match[1].isdigit():
                        end_year = int(match[1])
                    elif "present" in match[1].lower():
                        end_year = current_year
                elif "since" in match[0].lower() and match[1].isdigit():
                    start_year = int(match[1])
                    end_year = current_year
                    
                if start_year and end_year:
                    total_years += (end_year - start_year)
        
        return total_years

class FixedJobMatcher:
    def __init__(self, job_requirements):
        self.requirements = job_requirements
        
    def rank_resumes(self, resumes):
        """Rank resumes based on how well they match job requirements"""
        scored_resumes = []
        
        for resume in resumes:
            score = self._calculate_match_score(resume)
            scored_resumes.append((resume, score))
        
        return sorted(scored_resumes, key=lambda x: x[1], reverse=True)
    
    def _calculate_match_score(self, resume):
        """Calculate a match score between resume and job requirements"""
        score = 0
        
        # Skills matching (50% of total score)
        required_skills = set(skill.lower() for skill in self.requirements["required_skills"])
        candidate_skills = set(skill.lower() for skill in resume["skills"])
        
        skill_matches = required_skills.intersection(candidate_skills)
        skill_score = len(skill_matches) / max(1, len(required_skills))
        score += skill_score * 50
        
        # Education matching (25% of total score)
        education_score = self._match_education(resume["education"])
        score += education_score * 25
        
        # Experience matching (25% of total score)
        experience_score = self._match_experience(resume["experience"])
        score += experience_score * 25
        
        return score
    
    def _match_education(self, education_list):
        """Match education requirements"""
        if not education_list:
            return 0
        
        required_education = [edu.lower() for edu in self.requirements.get("education", [])]
        if not required_education:
            return 1
        
        education_text = " ".join(education_list).lower()
        matches = 0
        
        for req in required_education:
            if req in education_text:
                matches += 1
        
        return matches / len(required_education) if required_education else 0

    def _match_experience(self, years_experience):
        """Match experience requirements"""
        required_years = self.requirements.get("experience", 0)
        
        if required_years == 0:
            return 1
        
        if years_experience >= required_years:
            return 1
        elif years_experience >= required_years * 0.7:
            return 0.7
        elif years_experience > 0:
            return 0.3
        else:
            return 0

class FixedResumeParserGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 Resume Parser & Analyzer - Fixed Version")
        self.root.geometry("950x800")

        # Create main frame
        main_frame = ttk.Frame(root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="🔍 Resume Parser & Analyzer - Fixed Version",
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 25))

        # Status label
        status_label = ttk.Label(main_frame, text="✅ All dependencies loaded successfully!",
                                font=("Arial", 10), foreground="green")
        status_label.grid(row=1, column=0, columnspan=2, pady=(0, 15))

        # Resume folder section
        folder_frame = ttk.LabelFrame(main_frame, text="📁 Resume Files", padding="15")
        folder_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        folder_frame.columnconfigure(1, weight=1)

        ttk.Label(folder_frame, text="Resumes Folder:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W)
        self.folder_path = tk.StringVar(value="resumes/")
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_path, width=60, font=("Arial", 10))
        folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5))

        browse_btn = ttk.Button(folder_frame, text="Browse", command=self.browse_folder)
        browse_btn.grid(row=0, column=2, padx=(5, 0))

        # File count label
        self.file_count_label = ttk.Label(folder_frame, text="", font=("Arial", 9))
        self.file_count_label.grid(row=1, column=0, columnspan=3, pady=(8, 0))
        self.update_file_count()

        # Job requirements section
        req_frame = ttk.LabelFrame(main_frame, text="⚙️ Job Requirements", padding="15")
        req_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        req_frame.columnconfigure(1, weight=1)

        # Skills
        ttk.Label(req_frame, text="Required Skills:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W)
        self.skills_var = tk.StringVar(value="python, machine learning, data analysis, sql")
        skills_entry = ttk.Entry(req_frame, textvariable=self.skills_var, width=70, font=("Arial", 10))
        skills_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))

        ttk.Label(req_frame, text="(comma-separated)", font=("Arial", 8), foreground="gray").grid(row=1, column=1, sticky=tk.W, padx=(10, 0))

        # Education
        ttk.Label(req_frame, text="Education:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        self.education_var = tk.StringVar(value="bachelor, computer science")
        education_entry = ttk.Entry(req_frame, textvariable=self.education_var, width=70, font=("Arial", 10))
        education_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(10, 0))

        # Experience
        ttk.Label(req_frame, text="Min Experience (years):", font=("Arial", 10, "bold")).grid(row=3, column=0, sticky=tk.W, pady=(10, 0))
        self.experience_var = tk.StringVar(value="2")
        experience_entry = ttk.Entry(req_frame, textvariable=self.experience_var, width=10, font=("Arial", 10))
        experience_entry.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(0, 15))

        # Analyze button
        self.analyze_btn = ttk.Button(button_frame, text="🚀 Analyze Resumes",
                                     command=self.analyze_resumes)
        self.analyze_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Clear button
        clear_btn = ttk.Button(button_frame, text="🗑️ Clear Results", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT)

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="📊 Results", padding="15")
        results_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Results text area with scrollbar
        self.results_text = scrolledtext.ScrolledText(results_frame, height=20, width=90,
                                                     font=("Consolas", 10), wrap=tk.WORD)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure main frame row weights
        main_frame.rowconfigure(6, weight=1)

        # Add some initial help text
        self.results_text.insert(tk.END, "Welcome to Resume Parser & Analyzer - Fixed Version! 🎉\n\n")
        self.results_text.insert(tk.END, "✅ All import issues have been resolved!\n")
        self.results_text.insert(tk.END, "✅ All required packages are properly loaded!\n\n")
        self.results_text.insert(tk.END, "Instructions:\n")
        self.results_text.insert(tk.END, "1. Make sure your PDF/DOCX resumes are in the 'resumes' folder\n")
        self.results_text.insert(tk.END, "2. Customize the job requirements above\n")
        self.results_text.insert(tk.END, "3. Click 'Analyze Resumes' to start processing\n\n")
        self.results_text.insert(tk.END, "The app will rank candidates based on how well they match your requirements.\n")
        self.results_text.insert(tk.END, "=" * 80 + "\n\n")
