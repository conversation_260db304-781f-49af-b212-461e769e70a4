#!/usr/bin/env python3
"""
Resume Parser GUI - Fixed Version
A user-friendly graphical interface for the resume parser.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import os
import sys
import threading
import subprocess
import re
import importlib

print("🚀 Starting Resume Parser GUI...")

# Suppress Tkinter deprecation warning
os.environ['TK_SILENCE_DEPRECATION'] = '1'

def install_package_if_needed(package_name, import_name=None):
    """Install package if missing and return the module"""
    if import_name is None:
        import_name = package_name

    try:
        return importlib.import_module(import_name)
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name],
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            # Reload site packages
            import site
            importlib.reload(site)
            return importlib.import_module(import_name)
        except Exception as e:
            print(f"❌ Failed to install {package_name}: {e}")
            return None

# Install required packages
print("🔍 Checking dependencies...")
PyPDF2 = install_package_if_needed("PyPDF2")
docx = install_package_if_needed("python-docx", "docx")
nltk = install_package_if_needed("nltk")

# Handle NLTK imports
word_tokenize = None
stopwords = None

if nltk:
    try:
        from nltk.tokenize import word_tokenize
        from nltk.corpus import stopwords

        # Download NLTK data if needed
        try:
            nltk.data.find('tokenizers/punkt')
            nltk.data.find('corpora/stopwords')
        except LookupError:
            print("📥 Downloading NLTK data...")
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
    except Exception as e:
        print(f"⚠️ NLTK import warning: {e}")

print("✅ Dependencies loaded!")

# Define ResumeParser class inline to avoid import issues
class FixedResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        self.stop_words = set(stopwords.words('english')) if stopwords else set()

    def parse(self):
        """Parse resume file and extract relevant information"""
        text = self._extract_text()

        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "raw_text": text
        }

    def _extract_text(self):
        """Extract text from file based on file type"""
        if self.file_path.endswith('.pdf'):
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx'):
            return self._extract_text_from_docx()
        return ""

    def _extract_text_from_pdf(self):
        """Extract text from PDF files"""
        if not PyPDF2:
            return "PDF processing unavailable"

        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            text = f"Error reading PDF: {e}"
        return text

    def _extract_text_from_docx(self):
        """Extract text from DOCX files"""
        if not docx:
            return "DOCX processing unavailable"

        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except Exception as e:
            text = f"Error reading DOCX: {e}"
        return text

    def _extract_name(self, text):
        """Extract candidate name from resume text"""
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5 and len(line) > 3:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        """Extract email address from resume text"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        return emails[0] if emails else "Not found"

    def _extract_phone(self, text):
        """Extract phone number from resume text"""
        phone_pattern = r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}'
        phones = re.findall(phone_pattern, text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "Not found"

    def _extract_skills(self, text):
        """Extract skills from resume text"""
        common_skills = [
            "python", "java", "javascript", "html", "css", "sql", "nosql", "mongodb",
            "react", "angular", "vue", "node.js", "express", "django", "flask",
            "aws", "azure", "gcp", "docker", "kubernetes", "ci/cd", "git",
            "machine learning", "data analysis", "data science", "artificial intelligence",
            "nlp", "computer vision", "deep learning", "tensorflow", "pytorch",
            "agile", "scrum", "project management", "leadership"
        ]

        skills = []
        text_lower = text.lower()

        for skill in common_skills:
            if re.search(r'\b' + re.escape(skill) + r'\b', text_lower):
                skills.append(skill)

        return skills

    def _extract_education(self, text):
        """Extract education information from resume text"""
        education = []
        text_lower = text.lower()

        degree_keywords = ["bachelor", "master", "phd", "doctorate", "bs", "ms", "ba", "ma", "mba"]
        for keyword in degree_keywords:
            if keyword in text_lower:
                education.append(f"{keyword.title()} degree")

        return education

    def _extract_experience(self, text):
        """Extract work experience information and estimate years"""
        # Look for year patterns
        year_patterns = [
            r'(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience|exp)',
            r'(\d{4})\s*[-–—to]\s*(\d{4}|\bpresent\b)',
        ]

        total_years = 0
        current_year = 2024

        for pattern in year_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches:
                if isinstance(match, tuple):
                    if match[1].isdigit():
                        years = int(match[1]) - int(match[0])
                    else:
                        years = current_year - int(match[0])
                else:
                    years = int(match)
                total_years = max(total_years, years)

        return total_years

# Define JobMatcher class inline
class FixedJobMatcher:
    def __init__(self, job_requirements):
        self.requirements = job_requirements

    def rank_resumes(self, resumes):
        """Rank resumes based on how well they match job requirements"""
        scored_resumes = []

        for resume in resumes:
            score = self._calculate_match_score(resume)
            scored_resumes.append((resume, score))

        return sorted(scored_resumes, key=lambda x: x[1], reverse=True)

    def _calculate_match_score(self, resume):
        """Calculate a match score between resume and job requirements"""
        score = 0

        # Skills matching (50% of total score)
        required_skills = set(skill.lower() for skill in self.requirements["required_skills"])
        candidate_skills = set(skill.lower() for skill in resume["skills"])

        skill_matches = required_skills.intersection(candidate_skills)
        skill_score = len(skill_matches) / max(1, len(required_skills))
        score += skill_score * 50

        # Education matching (25% of total score)
        education_score = self._match_education(resume["education"])
        score += education_score * 25

        # Experience matching (25% of total score)
        experience_score = self._match_experience(resume["experience"])
        score += experience_score * 25

        return score

    def _match_education(self, education_list):
        """Match education requirements"""
        if not education_list:
            return 0

        required_education = [edu.lower() for edu in self.requirements.get("education", [])]
        if not required_education:
            return 1

        education_text = " ".join(education_list).lower()
        matches = 0

        for req in required_education:
            if req in education_text:
                matches += 1

        return matches / len(required_education) if required_education else 0

    def _match_experience(self, years_experience):
        """Match experience requirements"""
        required_years = self.requirements.get("experience", 0)

        if required_years == 0:
            return 1

        if years_experience >= required_years:
            return 1
        elif years_experience >= required_years * 0.7:
            return 0.7
        elif years_experience > 0:
            return 0.3
        else:
            return 0

class ResumeParserGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Resume Parser & Analyzer")
        self.root.geometry("900x750")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 Resume Parser & Analyzer", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 25))
        
        # Resume folder section
        folder_frame = ttk.LabelFrame(main_frame, text="📁 Resume Files", padding="15")
        folder_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        folder_frame.columnconfigure(1, weight=1)
        
        ttk.Label(folder_frame, text="Resumes Folder:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W)
        self.folder_path = tk.StringVar(value="resumes/")
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_path, width=60, font=("Arial", 10))
        folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        
        browse_btn = ttk.Button(folder_frame, text="Browse", command=self.browse_folder)
        browse_btn.grid(row=0, column=2, padx=(5, 0))
        
        # File count label
        self.file_count_label = ttk.Label(folder_frame, text="", font=("Arial", 9))
        self.file_count_label.grid(row=1, column=0, columnspan=3, pady=(8, 0))
        self.update_file_count()
        
        # Job requirements section
        req_frame = ttk.LabelFrame(main_frame, text="⚙️ Job Requirements", padding="15")
        req_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        req_frame.columnconfigure(1, weight=1)
        
        # Skills
        ttk.Label(req_frame, text="Required Skills:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W)
        self.skills_var = tk.StringVar(value="python, machine learning, data analysis, sql")
        skills_entry = ttk.Entry(req_frame, textvariable=self.skills_var, width=70, font=("Arial", 10))
        skills_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        ttk.Label(req_frame, text="(comma-separated)", font=("Arial", 8), foreground="gray").grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Education
        ttk.Label(req_frame, text="Education:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        self.education_var = tk.StringVar(value="bachelor, computer science")
        education_entry = ttk.Entry(req_frame, textvariable=self.education_var, width=70, font=("Arial", 10))
        education_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(10, 0))
        
        # Experience
        ttk.Label(req_frame, text="Min Experience (years):", font=("Arial", 10, "bold")).grid(row=3, column=0, sticky=tk.W, pady=(10, 0))
        self.experience_var = tk.StringVar(value="2")
        experience_entry = ttk.Entry(req_frame, textvariable=self.experience_var, width=10, font=("Arial", 10))
        experience_entry.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(0, 15))
        
        # Analyze button
        self.analyze_btn = ttk.Button(button_frame, text="🚀 Analyze Resumes", 
                                     command=self.analyze_resumes)
        self.analyze_btn.pack(side=tk.LEFT, padx=(0, 15))
        
        # Clear button
        clear_btn = ttk.Button(button_frame, text="🗑️ Clear Results", command=self.clear_results)
        clear_btn.pack(side=tk.LEFT)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="📊 Results", padding="15")
        results_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Results text area with scrollbar
        self.results_text = scrolledtext.ScrolledText(results_frame, height=22, width=90, 
                                                     font=("Consolas", 10), wrap=tk.WORD)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure main frame row weights
        main_frame.rowconfigure(5, weight=1)
        
        # Add some initial help text
        self.results_text.insert(tk.END, "Welcome to Resume Parser & Analyzer! 🎉\n\n")
        self.results_text.insert(tk.END, "Instructions:\n")
        self.results_text.insert(tk.END, "1. Make sure your PDF/DOCX resumes are in the 'resumes' folder\n")
        self.results_text.insert(tk.END, "2. Customize the job requirements above\n")
        self.results_text.insert(tk.END, "3. Click 'Analyze Resumes' to start processing\n\n")
        self.results_text.insert(tk.END, "The app will rank candidates based on how well they match your requirements.\n")
        self.results_text.insert(tk.END, "=" * 80 + "\n\n")
        
    def browse_folder(self):
        folder = filedialog.askdirectory(title="Select Resume Folder")
        if folder:
            self.folder_path.set(folder)
            self.update_file_count()
    
    def update_file_count(self):
        folder = self.folder_path.get()
        if os.path.exists(folder):
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            count = len(files)
            self.file_count_label.config(text=f"✅ Found {count} resume files (.pdf and .docx)")
        else:
            self.file_count_label.config(text="❌ Folder not found")
    
    def clear_results(self):
        self.results_text.delete(1.0, tk.END)
    
    def analyze_resumes(self):
        # Validate inputs
        folder = self.folder_path.get()
        if not os.path.exists(folder):
            messagebox.showerror("Error", "Resume folder does not exist!")
            return
        
        try:
            int(self.experience_var.get())  # Validate that it's a number
        except ValueError:
            messagebox.showerror("Error", "Experience must be a valid number!")
            return
        
        # Disable button and start progress
        self.analyze_btn.config(state='disabled', text="🔄 Processing...")
        self.progress.start()
        self.clear_results()
        
        # Run analysis in separate thread to prevent GUI freezing
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
    
    def run_analysis(self):
        try:
            folder = self.folder_path.get()
            
            # Parse job requirements
            skills_text = self.skills_var.get()
            education_text = self.education_var.get()
            experience_years = int(self.experience_var.get())
            
            skills_list = [skill.strip() for skill in skills_text.split(',') if skill.strip()]
            education_list = [edu.strip() for edu in education_text.split(',') if edu.strip()]
            
            job_requirements = {
                "required_skills": skills_list,
                "education": education_list,
                "experience": experience_years
            }
            
            # Find and process resumes
            resume_files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            
            if not resume_files:
                self.update_results("❌ No PDF or DOCX files found in the selected folder.")
                return
            
            parsed_resumes = []
            total_files = len(resume_files)
            
            self.update_results(f"🔍 Found {total_files} resume files. Processing...\n")
            
            for i, filename in enumerate(resume_files):
                file_path = os.path.join(folder, filename)
                self.update_results(f"📄 Processing: {filename}")
                
                try:
                    parser = FixedResumeParser(file_path)
                    resume_data = parser.parse()
                    resume_data['filename'] = filename
                    parsed_resumes.append(resume_data)
                except Exception as e:
                    self.update_results(f"❌ Error processing {filename}: {str(e)}")
            
            if not parsed_resumes:
                self.update_results("\n❌ No resumes were successfully processed.")
                return
            
            # Display job requirements
            self.update_results(f"\n⚙️ Job Requirements:")
            self.update_results(f"   Skills: {', '.join(skills_list)}")
            self.update_results(f"   Education: {', '.join(education_list)}")
            self.update_results(f"   Experience: {experience_years} years")
            self.update_results("=" * 80)
            
            # Match and rank resumes
            matcher = FixedJobMatcher(job_requirements)
            ranked_resumes = matcher.rank_resumes(parsed_resumes)
            
            # Display results
            self.update_results(f"\n🏆 Top matching resumes (out of {len(parsed_resumes)} processed):")
            self.update_results("=" * 80)
            
            for i, (resume, score) in enumerate(ranked_resumes, 1):
                # Score emoji
                if score >= 80:
                    score_emoji = "🥇"
                elif score >= 60:
                    score_emoji = "🥈"
                elif score >= 40:
                    score_emoji = "🥉"
                else:
                    score_emoji = "📋"
                
                self.update_results(f"\n{score_emoji} {i}. {resume['name']} ({resume['filename']})")
                self.update_results(f"   📊 Match Score: {score:.1f}/100")
                self.update_results(f"   📧 Email: {resume['email']}")
                self.update_results(f"   📞 Phone: {resume['phone']}")
                
                # Show skills (limit to first 8)
                skills_display = ', '.join(resume['skills'][:8])
                if len(resume['skills']) > 8:
                    skills_display += f" ... and {len(resume['skills']) - 8} more"
                self.update_results(f"   🛠️  Skills: {skills_display}")
                
                # Show education (limit to first 2)
                education_display = '; '.join(resume['education'][:2])
                if len(resume['education']) > 2:
                    education_display += " ..."
                self.update_results(f"   🎓 Education: {education_display}")
                
                self.update_results(f"   💼 Experience: {resume['experience']} years")
                self.update_results("-" * 80)
            
        except Exception as e:
            self.update_results(f"\n❌ Error during analysis: {str(e)}")
        finally:
            # Re-enable button and stop progress
            self.root.after(0, self.analysis_complete)
    
    def update_results(self, text):
        # Thread-safe way to update GUI
        self.root.after(0, lambda: self._append_text(text))
    
    def _append_text(self, text):
        self.results_text.insert(tk.END, text + "\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()
    
    def analysis_complete(self):
        self.progress.stop()
        self.analyze_btn.config(state='normal', text="🚀 Analyze Resumes")
        self.update_file_count()

def main():
    print("🎯 Creating main window...")
    try:
        root = tk.Tk()
        root.withdraw()  # Hide window initially

        print("🎨 Setting up GUI...")
        app = ResumeParserGUI(root)

        print("🖼️ Showing window...")
        root.deiconify()  # Show window after setup
        root.lift()  # Bring to front
        root.focus_force()  # Force focus

        print("🚀 Starting main loop...")
        root.mainloop()

    except Exception as e:
        print(f"❌ GUI Error: {e}")
        import traceback
        traceback.print_exc()

        # Show error dialog if possible
        try:
            messagebox.showerror("Error", f"Failed to start GUI: {e}")
        except:
            pass

    print("👋 Application finished.")

if __name__ == "__main__":
    main()
