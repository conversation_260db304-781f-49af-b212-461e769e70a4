#!/usr/bin/env python3
"""
macOS Resume Parser GUI
Fixed version specifically for macOS with proper window handling.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import threading
import subprocess
import re
import time

print("Starting macOS Resume Parser...")

# macOS-specific fixes
os.environ['TK_SILENCE_DEPRECATION'] = '1'

def install_package(package):
    try:
        __import__(package)
        return True
    except ImportError:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            return True
        except:
            return False

# Load packages
PyPDF2 = None
docx = None

try:
    if install_package("PyPDF2"):
        import PyPDF2
    if install_package("python-docx"):
        import docx
    print("Packages loaded!")
except Exception as e:
    print(f"Package warning: {e}")

class MacOSResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "filename": os.path.basename(self.file_path)
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf') and PyPDF2:
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx') and docx:
            return self._extract_text_from_docx()
        return f"File: {os.path.basename(self.file_path)}"
    
    def _extract_text_from_pdf(self):
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            text = f"Error reading PDF: {e}"
        return text

    def _extract_text_from_docx(self):
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except Exception as e:
            text = f"Error reading DOCX: {e}"
        return text

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "No email found"

    def _extract_phone(self, text):
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "No phone found"

    def _extract_skills(self, text):
        skills = [
            "python", "java", "javascript", "html", "css", "sql", "react", "angular", 
            "aws", "azure", "docker", "git", "machine learning", "data analysis",
            "project management", "excel", "communication", "leadership"
        ]
        
        found = []
        text_lower = text.lower()
        for skill in skills:
            if skill in text_lower:
                found.append(skill)
        
        return found if found else ["General skills"]

    def _extract_education(self, text):
        degrees = ["bachelor", "master", "phd", "college", "university"]
        found = []
        text_lower = text.lower()
        for degree in degrees:
            if degree in text_lower:
                found.append(degree.title())
        return found if found else ["Education"]

    def _extract_experience(self, text):
        years = re.findall(r'(\d+)\s*(?:years?|yrs?)', text.lower())
        return max([int(y) for y in years], default=1)

class MacOSJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = self._calculate_score(resume)
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)
    
    def _calculate_score(self, resume):
        score = 0
        required_skills = set(s.lower().strip() for s in self.requirements.get("required_skills", []))
        candidate_skills = set(s.lower() for s in resume["skills"])
        
        if required_skills:
            matches = required_skills.intersection(candidate_skills)
            score += (len(matches) / len(required_skills)) * 70
        
        if resume["education"]:
            score += 15
        
        if resume["experience"] >= self.requirements.get("experience", 0):
            score += 15
        
        return min(score, 100)

class MacOSGUI:
    def __init__(self, root):
        self.root = root
        
        # macOS-specific window setup
        self.setup_macos_window()
        
        # Variables
        self.folder_var = tk.StringVar(value="resumes/")
        self.skills_var = tk.StringVar(value="python, machine learning, sql")
        self.experience_var = tk.StringVar(value="2")
        
        # Create widgets after window setup
        self.root.after(100, self.create_widgets)
        
    def setup_macos_window(self):
        """Setup window specifically for macOS"""
        self.root.title("Resume Parser - macOS Version")
        
        # Force window to be visible on macOS
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)
        
        # Set window size and position
        self.root.geometry("950x750")
        
        # Center window on screen
        self.root.update_idletasks()
        width = 950
        height = 750
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
        # Configure background
        self.root.configure(bg='white')
        
        # Force focus
        self.root.focus_force()
        
        print("macOS window setup complete")
        
    def create_widgets(self):
        """Create all GUI widgets"""
        print("Creating widgets...")
        
        # Use regular tk widgets instead of ttk for better macOS compatibility
        main_frame = tk.Frame(self.root, bg='white', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="Resume Parser - macOS Version", 
                              font=("Arial", 18, "bold"), bg='white', fg='#333')
        title_label.pack(pady=(0, 10))
        
        # Status
        status_label = tk.Label(main_frame, text="macOS GUI with working file browser!", 
                               font=("Arial", 12), bg='white', fg='green')
        status_label.pack(pady=(0, 20))
        
        # Folder section
        folder_frame = tk.LabelFrame(main_frame, text="Resume Folder", bg='white', 
                                    font=("Arial", 12, "bold"), padx=15, pady=15)
        folder_frame.pack(fill='x', pady=(0, 15))
        
        folder_inner = tk.Frame(folder_frame, bg='white')
        folder_inner.pack(fill='x')
        
        tk.Label(folder_inner, text="Folder:", bg='white', font=("Arial", 11)).pack(side='left')
        
        self.folder_entry = tk.Entry(folder_inner, textvariable=self.folder_var, 
                                    width=60, font=("Arial", 11))
        self.folder_entry.pack(side='left', padx=(10, 10), fill='x', expand=True)
        
        # Browse button - this will open file explorer!
        browse_btn = tk.Button(folder_inner, text="Browse Folder", 
                              command=self.browse_folder, bg='#007bff', fg='white',
                              font=("Arial", 11), padx=15, pady=5)
        browse_btn.pack(side='right')
        
        # File count
        self.file_count_label = tk.Label(folder_frame, text="", bg='white', 
                                        font=("Arial", 10), fg='gray')
        self.file_count_label.pack(pady=(10, 0))
        
        # Check files button
        check_btn = tk.Button(folder_frame, text="Check Files", command=self.check_files,
                             bg='#28a745', fg='white', font=("Arial", 11), padx=15, pady=5)
        check_btn.pack(pady=(10, 0))
        
        # Requirements section
        req_frame = tk.LabelFrame(main_frame, text="Job Requirements", bg='white',
                                 font=("Arial", 12, "bold"), padx=15, pady=15)
        req_frame.pack(fill='x', pady=(0, 15))
        
        # Skills
        skills_inner = tk.Frame(req_frame, bg='white')
        skills_inner.pack(fill='x', pady=5)
        
        tk.Label(skills_inner, text="Required Skills:", bg='white', 
                font=("Arial", 11)).pack(side='left')
        
        self.skills_entry = tk.Entry(skills_inner, textvariable=self.skills_var, 
                                    width=50, font=("Arial", 11))
        self.skills_entry.pack(side='right', fill='x', expand=True, padx=(10, 0))
        
        # Experience
        exp_inner = tk.Frame(req_frame, bg='white')
        exp_inner.pack(fill='x', pady=5)
        
        tk.Label(exp_inner, text="Min Experience (years):", bg='white', 
                font=("Arial", 11)).pack(side='left')
        
        self.exp_entry = tk.Entry(exp_inner, textvariable=self.experience_var, 
                                 width=10, font=("Arial", 11))
        self.exp_entry.pack(side='right', padx=(10, 0))
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(pady=(0, 15))
        
        self.analyze_btn = tk.Button(button_frame, text="Analyze Resumes", 
                                    command=self.analyze_resumes, bg='#dc3545', fg='white',
                                    font=("Arial", 12, "bold"), padx=20, pady=10)
        self.analyze_btn.pack(side='left', padx=(0, 10))
        
        clear_btn = tk.Button(button_frame, text="Clear Results", command=self.clear_results,
                             bg='#6c757d', fg='white', font=("Arial", 11), padx=15, pady=8)
        clear_btn.pack(side='left')
        
        # Results
        results_frame = tk.LabelFrame(main_frame, text="Results", bg='white',
                                     font=("Arial", 12, "bold"), padx=15, pady=15)
        results_frame.pack(fill='both', expand=True)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=15, width=80, 
                                                     font=("Monaco", 10), wrap='word')
        self.results_text.pack(fill='both', expand=True)
        
        # Initial message
        self.results_text.insert(tk.END, "Welcome to Resume Parser - macOS Version!\n\n")
        self.results_text.insert(tk.END, "Features:\n")
        self.results_text.insert(tk.END, "- Working 'Browse Folder' button opens Finder\n")
        self.results_text.insert(tk.END, "- 'Check Files' shows resume count\n")
        self.results_text.insert(tk.END, "- Full resume analysis and ranking\n")
        self.results_text.insert(tk.END, "- Optimized for macOS\n\n")
        self.results_text.insert(tk.END, "Instructions:\n")
        self.results_text.insert(tk.END, "1. Click 'Browse Folder' to select resume folder\n")
        self.results_text.insert(tk.END, "2. Click 'Check Files' to see resume count\n")
        self.results_text.insert(tk.END, "3. Customize job requirements\n")
        self.results_text.insert(tk.END, "4. Click 'Analyze Resumes'\n\n")
        self.results_text.insert(tk.END, "Ready to analyze resumes!\n")
        
        # Auto-check files
        self.root.after(1000, self.check_files)
        
        print("Widgets created successfully")
    
    def browse_folder(self):
        """Open Finder to select folder - works on macOS!"""
        try:
            folder = filedialog.askdirectory(
                title="Select Resume Folder",
                initialdir=self.folder_var.get() if os.path.exists(self.folder_var.get()) else os.path.expanduser("~")
            )
            
            if folder:
                self.folder_var.set(folder)
                self.check_files()
                self.add_result(f"Selected folder: {folder}")
                print(f"Selected folder: {folder}")
        except Exception as e:
            print(f"Error in browse_folder: {e}")
            messagebox.showerror("Error", f"Could not open folder browser: {e}")
    
    def check_files(self):
        """Check how many resume files are in the folder"""
        try:
            folder = self.folder_var.get()
            
            if os.path.exists(folder):
                files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
                count = len(files)
                self.file_count_label.config(text=f"Found {count} resume files (.pdf and .docx)")
                
                if count > 0:
                    self.file_count_label.config(fg="green")
                else:
                    self.file_count_label.config(fg="orange")
            else:
                self.file_count_label.config(text="Folder not found", fg="red")
        except Exception as e:
            print(f"Error checking files: {e}")
            self.file_count_label.config(text=f"Error: {e}", fg="red")
    
    def analyze_resumes(self):
        """Analyze all resumes in the folder"""
        try:
            self.analyze_btn.config(state='disabled', text="Processing...")
            self.clear_results()
            
            # Run in separate thread to prevent GUI freezing
            thread = threading.Thread(target=self.run_analysis)
            thread.daemon = True
            thread.start()
        except Exception as e:
            print(f"Error starting analysis: {e}")
            self.add_result(f"Error starting analysis: {e}")
            self.analyze_btn.config(state='normal', text="Analyze Resumes")
    
    def run_analysis(self):
        try:
            folder = self.folder_var.get()
            
            if not os.path.exists(folder):
                self.add_result("Error: Folder does not exist!")
                return
            
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            if not files:
                self.add_result("Error: No PDF or DOCX files found!")
                return
            
            self.add_result(f"Found {len(files)} resume files. Processing...\n")
            
            # Parse requirements
            skills_text = self.skills_var.get()
            skills = [s.strip() for s in skills_text.split(',') if s.strip()]
            experience = int(self.experience_var.get() or 0)
            
            requirements = {
                "required_skills": skills,
                "experience": experience
            }
            
            self.add_result(f"Looking for skills: {', '.join(skills)}")
            self.add_result(f"Minimum experience: {experience} years\n")
            
            # Process resumes
            resumes = []
            for i, filename in enumerate(files, 1):
                self.add_result(f"Processing {i}/{len(files)}: {filename}")
                try:
                    parser = MacOSResumeParser(os.path.join(folder, filename))
                    resume_data = parser.parse()
                    resumes.append(resume_data)
                except Exception as e:
                    self.add_result(f"  Error: {str(e)}")
            
            if resumes:
                self.add_result(f"\nSuccessfully processed {len(resumes)} resumes")
                
                # Rank resumes
                matcher = MacOSJobMatcher(requirements)
                ranked = matcher.rank_resumes(resumes)
                
                self.add_result(f"\nRANKING RESULTS:")
                self.add_result("=" * 60)
                
                for i, (resume, score) in enumerate(ranked, 1):
                    self.add_result(f"\n{i}. {resume['name']}")
                    self.add_result(f"   Score: {score:.1f}/100")
                    self.add_result(f"   Email: {resume['email']}")
                    self.add_result(f"   Phone: {resume['phone']}")
                    self.add_result(f"   Skills: {', '.join(resume['skills'][:6])}")
                    if len(resume['skills']) > 6:
                        self.add_result(f"           ... and {len(resume['skills'])-6} more")
                    self.add_result(f"   Education: {', '.join(resume['education'][:3])}")
                    self.add_result(f"   Experience: {resume['experience']} years")
                    self.add_result("   " + "-" * 50)
            else:
                self.add_result("\nNo resumes were processed successfully")
                
        except Exception as e:
            self.add_result(f"\nError during analysis: {str(e)}")
            print(f"Analysis error: {e}")
        finally:
            # Re-enable button
            self.root.after(0, self.analysis_complete)
    
    def add_result(self, text):
        """Add text to results (thread-safe)"""
        def update():
            self.results_text.insert(tk.END, text + "\n")
            self.results_text.see(tk.END)
        self.root.after(0, update)
    
    def clear_results(self):
        """Clear the results area"""
        self.results_text.delete(1.0, tk.END)
    
    def analysis_complete(self):
        """Re-enable the analyze button"""
        self.analyze_btn.config(state='normal', text="Analyze Resumes")

def main():
    print("Creating macOS GUI...")
    
    # Create root window
    root = tk.Tk()
    
    # Hide window initially to prevent black screen
    root.withdraw()
    
    print("Setting up macOS-specific configurations...")
    
    # Create app instance
    app = MacOSGUI(root)
    
    # Show window after setup
    root.deiconify()
    root.lift()
    root.focus_force()
    
    print("macOS GUI ready and visible!")
    
    # Start main loop
    root.mainloop()

if __name__ == "__main__":
    main()
