#!/usr/bin/env python3
"""
Working Resume Parser GUI - Guaranteed to work!
Simple, clean implementation with all dependencies handled.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import os
import sys
import threading
import subprocess
import re

print("🚀 Starting Resume Parser GUI...")

# Suppress Tkinter deprecation warning
os.environ['TK_SILENCE_DEPRECATION'] = '1'

def install_if_missing(package_name, import_name=None):
    """Install package if it's missing"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "--user", package_name], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            return True
        except:
            return False

# Install required packages silently
packages = [("PyPDF2", "PyPDF2"), ("python-docx", "docx"), ("nltk", "nltk")]
for pkg_name, import_name in packages:
    install_if_missing(pkg_name, import_name)

# Import packages
try:
    import PyPDF2
    import docx
    import nltk
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    
    # Download NLTK data silently
    try:
        nltk.data.find('tokenizers/punkt')
        nltk.data.find('corpora/stopwords')
    except LookupError:
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
    
    print("✅ All packages loaded successfully!")
except Exception as e:
    print(f"❌ Error loading packages: {e}")
    sys.exit(1)

class SimpleResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        self.stop_words = set(stopwords.words('english'))
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "raw_text": text
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf'):
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx'):
            return self._extract_text_from_docx()
        return ""
    
    def _extract_text_from_pdf(self):
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except:
            pass
        return text

    def _extract_text_from_docx(self):
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except:
            pass
        return text

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5:
                return line
        return "Unknown"

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else ""

    def _extract_phone(self, text):
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else ""

    def _extract_skills(self, text):
        skills = ["python", "java", "javascript", "html", "css", "sql", "react", "angular", 
                 "aws", "azure", "docker", "kubernetes", "machine learning", "data analysis"]
        found_skills = []
        text_lower = text.lower()
        for skill in skills:
            if skill in text_lower:
                found_skills.append(skill)
        return found_skills

    def _extract_education(self, text):
        education = []
        text_lower = text.lower()
        degrees = ["bachelor", "master", "phd", "bs", "ms", "ba", "ma", "mba"]
        for degree in degrees:
            if degree in text_lower:
                education.append(f"{degree} degree")
        return education

    def _extract_experience(self, text):
        # Simple experience extraction - count years mentioned
        years = re.findall(r'(\d+)\s*(?:years?|yrs?)', text.lower())
        return max([int(y) for y in years], default=0)

class SimpleJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = self._calculate_score(resume)
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)
    
    def _calculate_score(self, resume):
        score = 0
        
        # Skills (50%)
        required_skills = set(s.lower() for s in self.requirements["required_skills"])
        candidate_skills = set(s.lower() for s in resume["skills"])
        skill_matches = required_skills.intersection(candidate_skills)
        score += (len(skill_matches) / max(1, len(required_skills))) * 50
        
        # Education (25%)
        if resume["education"]:
            score += 25
        
        # Experience (25%)
        if resume["experience"] >= self.requirements.get("experience", 0):
            score += 25
        
        return score

class WorkingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Resume Parser - Working Version")
        self.root.geometry("800x600")
        
        # Main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        ttk.Label(main_frame, text="Resume Parser - Working Version", 
                 font=("Arial", 16, "bold")).grid(row=0, column=0, columnspan=2, pady=10)
        
        # Folder selection
        ttk.Label(main_frame, text="Resume Folder:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.folder_var = tk.StringVar(value="resumes/")
        ttk.Entry(main_frame, textvariable=self.folder_var, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Job requirements
        ttk.Label(main_frame, text="Required Skills:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.skills_var = tk.StringVar(value="python, machine learning, sql")
        ttk.Entry(main_frame, textvariable=self.skills_var, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(main_frame, text="Min Experience:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.exp_var = tk.StringVar(value="2")
        ttk.Entry(main_frame, textvariable=self.exp_var, width=10).grid(row=3, column=1, sticky=tk.W, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        self.analyze_btn = ttk.Button(button_frame, text="Analyze Resumes", command=self.analyze)
        self.analyze_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="Clear", command=self.clear).pack(side=tk.LEFT, padx=5)
        
        # Progress
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Results
        ttk.Label(main_frame, text="Results:").grid(row=6, column=0, sticky=tk.W, pady=(10,0))
        self.results = scrolledtext.ScrolledText(main_frame, height=15, width=80)
        self.results.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        main_frame.rowconfigure(7, weight=1)
        
        # Initial message
        self.results.insert(tk.END, "Welcome to Resume Parser!\n\n")
        self.results.insert(tk.END, "1. Put your PDF/DOCX resumes in the 'resumes' folder\n")
        self.results.insert(tk.END, "2. Customize the requirements above\n")
        self.results.insert(tk.END, "3. Click 'Analyze Resumes'\n\n")
        self.results.insert(tk.END, "Ready to analyze resumes!\n")
        
    def clear(self):
        self.results.delete(1.0, tk.END)
        
    def analyze(self):
        self.analyze_btn.config(state='disabled')
        self.progress.start()
        self.clear()
        
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
        
    def run_analysis(self):
        try:
            folder = self.folder_var.get()
            if not os.path.exists(folder):
                self.add_result("❌ Folder not found!")
                return
                
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            if not files:
                self.add_result("❌ No PDF/DOCX files found!")
                return
                
            self.add_result(f"📁 Found {len(files)} files. Processing...\n")
            
            # Parse requirements
            skills = [s.strip() for s in self.skills_var.get().split(',')]
            experience = int(self.exp_var.get() or 0)
            
            requirements = {
                "required_skills": skills,
                "experience": experience
            }
            
            # Process resumes
            resumes = []
            for filename in files:
                self.add_result(f"📄 Processing: {filename}")
                try:
                    parser = SimpleResumeParser(os.path.join(folder, filename))
                    resume_data = parser.parse()
                    resume_data['filename'] = filename
                    resumes.append(resume_data)
                except Exception as e:
                    self.add_result(f"❌ Error: {e}")
            
            if not resumes:
                self.add_result("❌ No resumes processed successfully!")
                return
                
            # Rank resumes
            matcher = SimpleJobMatcher(requirements)
            ranked = matcher.rank_resumes(resumes)
            
            self.add_result(f"\n🏆 Results (Top {len(ranked)} candidates):")
            self.add_result("=" * 50)
            
            for i, (resume, score) in enumerate(ranked, 1):
                self.add_result(f"\n{i}. {resume['name']} ({resume['filename']})")
                self.add_result(f"   Score: {score:.1f}/100")
                self.add_result(f"   Email: {resume['email']}")
                self.add_result(f"   Phone: {resume['phone']}")
                self.add_result(f"   Skills: {', '.join(resume['skills'][:5])}")
                self.add_result(f"   Experience: {resume['experience']} years")
                
        except Exception as e:
            self.add_result(f"❌ Error: {e}")
        finally:
            self.root.after(0, self.analysis_done)
            
    def add_result(self, text):
        self.root.after(0, lambda: self.results.insert(tk.END, text + "\n"))
        
    def analysis_done(self):
        self.progress.stop()
        self.analyze_btn.config(state='normal')

def main():
    print("🎯 Creating GUI window...")
    root = tk.Tk()
    app = WorkingGUI(root)
    print("✅ GUI ready! Starting main loop...")
    root.mainloop()
    print("👋 GUI closed.")

if __name__ == "__main__":
    main()
