#!/usr/bin/env python3
"""
Auto-installer and launcher for Resume Parser GUI
This script automatically installs all required packages and launches the GUI.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        print(f"📦 Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--user", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def check_and_install_packages():
    """Check for required packages and install if missing"""
    packages = [
        "PyPDF2",
        "python-docx", 
        "nltk"
    ]
    
    print("🔍 Checking required packages...")
    
    for package in packages:
        try:
            if package == "python-docx":
                import docx
            elif package == "PyPDF2":
                import PyPDF2
            elif package == "nltk":
                import nltk
            print(f"✅ {package} is already installed")
        except ImportError:
            if not install_package(package):
                return False
    
    return True

def download_nltk_data():
    """Download required NLTK data"""
    try:
        import nltk
        print("📥 Downloading NLTK data...")
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        print("✅ NLTK data downloaded")
    except Exception as e:
        print(f"⚠️ Warning: Could not download NLTK data: {e}")

def main():
    print("🚀 Resume Parser Auto-Installer")
    print("=" * 50)
    
    # Install packages
    if not check_and_install_packages():
        print("❌ Failed to install required packages")
        print("💡 Try running manually: pip3 install --user PyPDF2 python-docx nltk")
        return
    
    # Download NLTK data
    download_nltk_data()
    
    print("\n✅ All packages installed successfully!")
    print("🚀 Launching GUI...")
    
    # Launch the simple GUI
    try:
        if os.path.exists("simple_gui.py"):
            subprocess.run([sys.executable, "simple_gui.py"])
        else:
            print("❌ simple_gui.py not found")
            print("💡 Please make sure you're in the correct directory")
    except Exception as e:
        print(f"❌ Error launching GUI: {e}")

if __name__ == "__main__":
    main()
