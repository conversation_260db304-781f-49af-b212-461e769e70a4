# Resume Parser App

A Python application that parses resumes from PDF and DOCX files, extracts key information, and ranks candidates based on job requirements.

## Features

- **Multi-format support**: Parses both PDF and DOCX resume files
- **Information extraction**: Extracts names, emails, phone numbers, skills, education, and experience
- **Keyword matching**: Identifies technical skills and qualifications
- **Ranking system**: Scores and ranks resumes based on job requirements
- **Flexible requirements**: Easy to customize job requirements

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. The app will automatically download NLTK data when first run.

## Usage

1. **Prepare your resumes**: 
   - Create a `resumes/` folder in the project directory
   - Add your PDF and DOCX resume files to this folder

2. **Customize job requirements** (optional):
   - Edit the `job_requirements` dictionary in `resume_parser/app.py`
   - Modify skills, education requirements, and experience years

3. **Run the application**:
```bash
python resume_parser/app.py
```

## Example Output

```
Found 5 resume files. Processing...
Processing: john_doe.pdf
Processing: jane_smith.docx
...

Job Requirements:
Skills: python, machine learning, data analysis, sql
Education: bachelor, computer science
Experience: 2 years
--------------------------------------------------

Top matching resumes (out of 5 processed):
============================================================
1. John Doe (john_doe.pdf)
   Match Score: 85.0/100
   Email: <EMAIL>
   Phone: (*************
   Skills: python, machine learning, sql, tensorflow, aws
   Education: Bachelor of Science in Computer Science
   Experience: 3 years
------------------------------------------------------------
```

## Customization

### Job Requirements

Edit the `job_requirements` dictionary in `app.py`:

```python
job_requirements = {
    "required_skills": ["python", "react", "javascript", "aws"],
    "education": ["bachelor", "engineering"],
    "experience": 3  # years
}
```

### Adding New Skills

Modify the `common_skills` list in `resume_parser.py` to include industry-specific skills.

## File Structure

```
AI Resume agent/
├── resume_parser/
│   ├── __init__.py
│   ├── app.py              # Main application
│   ├── resume_parser.py    # Resume parsing logic
│   └── job_matcher.py      # Matching and ranking logic
├── resumes/                # Place resume files here
├── requirements.txt        # Python dependencies
└── README.md              # This file
```

## How It Works

1. **Text Extraction**: Extracts text from PDF and DOCX files
2. **Information Parsing**: Uses regex and NLP to identify:
   - Contact information (name, email, phone)
   - Technical skills from predefined lists
   - Education details and degrees
   - Work experience duration
3. **Scoring**: Calculates match scores based on:
   - Skills matching (50% weight)
   - Education requirements (25% weight)
   - Experience requirements (25% weight)
4. **Ranking**: Sorts candidates by total match score

## Dependencies

- `PyPDF2`: PDF text extraction
- `python-docx`: DOCX text extraction  
- `nltk`: Natural language processing for text analysis

## Limitations

- Text extraction quality depends on PDF/DOCX formatting
- Skills detection is based on predefined keywords
- Experience calculation relies on date pattern recognition
- Name extraction uses simple heuristics

## Future Enhancements

- Add support for more file formats (TXT, RTF)
- Implement machine learning for better skill extraction
- Add web interface for easier use
- Include more sophisticated NLP for better parsing
- Add export functionality for results
