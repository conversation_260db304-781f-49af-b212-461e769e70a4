#!/usr/bin/env python3
"""
Basic Resume Parser GUI
Ultra-simple version to fix macOS display issues.
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import os
import sys
import threading
import subprocess
import re

print("Starting Basic Resume Parser...")

# Force software rendering on macOS
os.environ['TK_SILENCE_DEPRECATION'] = '1'
os.environ['DISPLAY'] = ':0'

def install_package(package):
    try:
        __import__(package)
        return True
    except ImportError:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            return True
        except:
            return False

# Load packages
PyPDF2 = None
docx = None

try:
    if install_package("PyPDF2"):
        import PyPDF2
    if install_package("python-docx"):
        import docx
    print("Packages ready!")
except:
    print("Using basic text processing")

class BasicResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "filename": os.path.basename(self.file_path)
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf') and PyPDF2:
            try:
                with open(self.file_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    text = ""
                    for page in reader.pages:
                        text += page.extract_text() + "\n"
                    return text
            except:
                pass
        elif self.file_path.endswith('.docx') and docx:
            try:
                doc = docx.Document(self.file_path)
                text = ""
                for para in doc.paragraphs:
                    text += para.text + "\n"
                return text
            except:
                pass
        return f"Resume: {os.path.basename(self.file_path)}"

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:3]:
            line = line.strip()
            if line and len(line.split()) <= 4:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "No email"

    def _extract_phone(self, text):
        phones = re.findall(r'(\d{3}[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return phones[0] if phones else "No phone"

    def _extract_skills(self, text):
        skills = ["python", "java", "javascript", "sql", "react", "aws", "excel"]
        found = []
        text_lower = text.lower()
        for skill in skills:
            if skill in text_lower:
                found.append(skill)
        return found if found else ["Skills"]

    def _extract_education(self, text):
        degrees = ["bachelor", "master", "phd", "college"]
        found = []
        text_lower = text.lower()
        for degree in degrees:
            if degree in text_lower:
                found.append(degree.title())
        return found if found else ["Education"]

    def _extract_experience(self, text):
        years = re.findall(r'(\d+)\s*(?:years?|yrs?)', text.lower())
        return max([int(y) for y in years], default=1)

class BasicJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = 0
            required_skills = set(s.lower().strip() for s in self.requirements.get("required_skills", []))
            candidate_skills = set(s.lower() for s in resume["skills"])
            
            if required_skills:
                matches = required_skills.intersection(candidate_skills)
                score += (len(matches) / len(required_skills)) * 100
            
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)

class BasicGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Resume Parser - Basic")
        self.root.geometry("700x500")
        
        # Use system default colors
        self.root.configure(bg='SystemButtonFace')
        
        # Variables
        self.folder_var = tk.StringVar(value="resumes/")
        self.skills_var = tk.StringVar(value="python, sql")
        self.experience_var = tk.StringVar(value="2")
        
        self.create_widgets()
        
    def create_widgets(self):
        # Use pack instead of grid for simplicity
        
        # Title
        tk.Label(self.root, text="Resume Parser - Basic Version", 
                font=("Arial", 14, "bold")).pack(pady=10)
        
        # Folder section
        folder_frame = tk.Frame(self.root)
        folder_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(folder_frame, text="Folder:").pack(side='left')
        
        self.folder_entry = tk.Entry(folder_frame, textvariable=self.folder_var, width=40)
        self.folder_entry.pack(side='left', padx=10)
        
        tk.Button(folder_frame, text="Browse", command=self.browse_folder).pack(side='left')
        
        # File count
        self.file_count_label = tk.Label(self.root, text="")
        self.file_count_label.pack(pady=5)
        
        tk.Button(self.root, text="Check Files", command=self.check_files).pack(pady=5)
        
        # Skills
        skills_frame = tk.Frame(self.root)
        skills_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(skills_frame, text="Skills:").pack(side='left')
        self.skills_entry = tk.Entry(skills_frame, textvariable=self.skills_var, width=40)
        self.skills_entry.pack(side='left', padx=10)
        
        # Experience
        exp_frame = tk.Frame(self.root)
        exp_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(exp_frame, text="Experience:").pack(side='left')
        self.exp_entry = tk.Entry(exp_frame, textvariable=self.experience_var, width=10)
        self.exp_entry.pack(side='left', padx=10)
        
        # Buttons
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        self.analyze_btn = tk.Button(button_frame, text="Analyze Resumes", 
                                    command=self.analyze_resumes)
        self.analyze_btn.pack(side='left', padx=10)
        
        tk.Button(button_frame, text="Clear", command=self.clear_results).pack(side='left', padx=10)
        
        # Results - simple text widget
        self.results_text = tk.Text(self.root, height=15, width=70, wrap='word')
        self.results_text.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Initial message
        self.results_text.insert('1.0', "Basic Resume Parser\n\n")
        self.results_text.insert('end', "1. Click Browse to select folder\n")
        self.results_text.insert('end', "2. Click Check Files\n")
        self.results_text.insert('end', "3. Edit skills and experience\n")
        self.results_text.insert('end', "4. Click Analyze Resumes\n\n")
        self.results_text.insert('end', "Ready!\n")
        
        # Auto-check files
        self.root.after(1000, self.check_files)
        
    def browse_folder(self):
        folder = filedialog.askdirectory(title="Select Resume Folder")
        if folder:
            self.folder_var.set(folder)
            self.check_files()
            self.add_result(f"Selected: {folder}")
    
    def check_files(self):
        folder = self.folder_var.get()
        if os.path.exists(folder):
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            self.file_count_label.config(text=f"Found {len(files)} files")
        else:
            self.file_count_label.config(text="Folder not found")
    
    def analyze_resumes(self):
        self.analyze_btn.config(state='disabled', text="Processing...")
        self.clear_results()
        
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
    
    def run_analysis(self):
        try:
            folder = self.folder_var.get()
            
            if not os.path.exists(folder):
                self.add_result("Error: Folder not found!")
                return
            
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            if not files:
                self.add_result("Error: No resume files found!")
                return
            
            self.add_result(f"Processing {len(files)} files...\n")
            
            skills = [s.strip() for s in self.skills_var.get().split(',') if s.strip()]
            experience = int(self.experience_var.get() or 0)
            
            requirements = {"required_skills": skills, "experience": experience}
            
            self.add_result(f"Looking for: {', '.join(skills)}")
            self.add_result(f"Min experience: {experience} years\n")
            
            resumes = []
            for i, filename in enumerate(files, 1):
                self.add_result(f"{i}/{len(files)}: {filename}")
                try:
                    parser = BasicResumeParser(os.path.join(folder, filename))
                    resume_data = parser.parse()
                    resumes.append(resume_data)
                except Exception as e:
                    self.add_result(f"  Error: {e}")
            
            if resumes:
                matcher = BasicJobMatcher(requirements)
                ranked = matcher.rank_resumes(resumes)
                
                self.add_result(f"\nRESULTS:")
                self.add_result("=" * 40)
                
                for i, (resume, score) in enumerate(ranked, 1):
                    self.add_result(f"\n{i}. {resume['name']}")
                    self.add_result(f"   Score: {score:.1f}/100")
                    self.add_result(f"   Email: {resume['email']}")
                    self.add_result(f"   Phone: {resume['phone']}")
                    self.add_result(f"   Skills: {', '.join(resume['skills'][:4])}")
                    self.add_result(f"   Education: {', '.join(resume['education'][:2])}")
                    self.add_result(f"   Experience: {resume['experience']} years")
                    self.add_result("   " + "-" * 30)
            else:
                self.add_result("\nNo resumes processed")
                
        except Exception as e:
            self.add_result(f"\nError: {e}")
        finally:
            self.root.after(0, self.analysis_complete)
    
    def add_result(self, text):
        def update():
            self.results_text.insert('end', text + "\n")
            self.results_text.see('end')
        self.root.after(0, update)
    
    def clear_results(self):
        self.results_text.delete('1.0', 'end')
    
    def analysis_complete(self):
        self.analyze_btn.config(state='normal', text="Analyze Resumes")
    
    def run(self):
        # Force window to display
        self.root.update()
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        
        print("Basic GUI should be visible now!")
        self.root.mainloop()

def main():
    print("Creating basic GUI...")
    
    try:
        app = BasicGUI()
        app.run()
    except Exception as e:
        print(f"GUI Error: {e}")
        # Fallback to terminal interface
        print("GUI failed, using terminal interface...")
        terminal_interface()

def terminal_interface():
    """Fallback terminal interface if GUI fails"""
    print("\n=== Resume Parser - Terminal Interface ===")
    
    folder = input("Enter resume folder path (default: resumes/): ").strip() or "resumes/"
    
    if not os.path.exists(folder):
        print("Folder not found!")
        return
    
    files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
    print(f"Found {len(files)} resume files")
    
    if not files:
        print("No resume files found!")
        return
    
    skills_input = input("Enter required skills (comma-separated): ").strip()
    skills = [s.strip() for s in skills_input.split(',') if s.strip()]
    
    experience = int(input("Enter minimum experience (years): ") or 0)
    
    print(f"\nProcessing {len(files)} files...")
    
    resumes = []
    for filename in files:
        print(f"Processing: {filename}")
        try:
            parser = BasicResumeParser(os.path.join(folder, filename))
            resume_data = parser.parse()
            resumes.append(resume_data)
        except Exception as e:
            print(f"  Error: {e}")
    
    if resumes:
        requirements = {"required_skills": skills, "experience": experience}
        matcher = BasicJobMatcher(requirements)
        ranked = matcher.rank_resumes(resumes)
        
        print(f"\nRESULTS:")
        print("=" * 50)
        
        for i, (resume, score) in enumerate(ranked, 1):
            print(f"\n{i}. {resume['name']}")
            print(f"   Score: {score:.1f}/100")
            print(f"   Email: {resume['email']}")
            print(f"   Skills: {', '.join(resume['skills'][:5])}")
            print(f"   Experience: {resume['experience']} years")
    else:
        print("No resumes processed successfully")

if __name__ == "__main__":
    main()
