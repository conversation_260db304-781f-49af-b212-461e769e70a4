#!/usr/bin/env python3
"""
AI Resume Analysis Agent
An intelligent agent that can reason about resumes and have conversations.
"""

import os
import sys
import json
import subprocess
import re
from datetime import datetime

print("🤖 Starting AI Resume Analysis Agent...")

# Install required packages
def install_package(package):
    try:
        __import__(package)
        return True
    except ImportError:
        print(f"📦 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            return True
        except:
            return False

# Load packages
PyPDF2 = None
docx = None
openai = None

try:
    if install_package("PyPDF2"):
        import PyPDF2
    if install_package("python-docx"):
        import docx
    if install_package("openai"):
        import openai
    print("✅ Packages loaded!")
except Exception as e:
    print(f"⚠️ Package warning: {e}")

class ResumeExtractor:
    """Extract and parse resume content"""
    
    def __init__(self, file_path):
        self.file_path = file_path
        
    def extract_text(self):
        """Extract text from PDF or DOCX files"""
        if self.file_path.endswith('.pdf') and PyPDF2:
            return self._extract_from_pdf()
        elif self.file_path.endswith('.docx') and docx:
            return self._extract_from_docx()
        return f"Could not extract text from {os.path.basename(self.file_path)}"
    
    def _extract_from_pdf(self):
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            text = f"Error reading PDF: {e}"
        return text
    
    def _extract_from_docx(self):
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
            # Extract from tables too
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                text += "\n"
        except Exception as e:
            text = f"Error reading DOCX: {e}"
        return text

class AIResumeAgent:
    """Intelligent AI agent for resume analysis and conversation"""
    
    def __init__(self):
        self.resumes_data = {}
        self.conversation_history = []
        self.current_folder = "resumes/"
        
        # Try to set up OpenAI (optional)
        self.openai_available = self._setup_openai()
        
        print("🤖 AI Resume Agent initialized!")
        print("💬 I can analyze resumes and have intelligent conversations about candidates.")
        
    def _setup_openai(self):
        """Try to set up OpenAI API"""
        try:
            # Check for API key in environment
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key and openai:
                openai.api_key = api_key
                return True
            else:
                print("💡 Tip: Set OPENAI_API_KEY environment variable for enhanced AI reasoning")
                return False
        except:
            return False
    
    def load_resumes(self, folder_path=None):
        """Load and analyze all resumes in the folder"""
        if folder_path:
            self.current_folder = folder_path
            
        if not os.path.exists(self.current_folder):
            return f"❌ Folder '{self.current_folder}' not found."
        
        files = [f for f in os.listdir(self.current_folder) 
                if f.endswith(('.pdf', '.docx'))]
        
        if not files:
            return f"❌ No resume files found in '{self.current_folder}'"
        
        print(f"📁 Loading {len(files)} resumes from '{self.current_folder}'...")
        
        self.resumes_data = {}
        
        for filename in files:
            file_path = os.path.join(self.current_folder, filename)
            print(f"📄 Processing: {filename}")
            
            try:
                extractor = ResumeExtractor(file_path)
                text = extractor.extract_text()
                
                # Parse resume data
                resume_data = self._parse_resume(text, filename)
                self.resumes_data[filename] = resume_data
                
            except Exception as e:
                print(f"❌ Error processing {filename}: {e}")
        
        return f"✅ Loaded {len(self.resumes_data)} resumes successfully!"
    
    def _parse_resume(self, text, filename):
        """Parse resume text into structured data"""
        return {
            "filename": filename,
            "raw_text": text,
            "name": self._extract_name(text, filename),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "summary": self._create_summary(text)
        }
    
    def _extract_name(self, text, filename):
        """Extract candidate name"""
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5 and len(line) > 3:
                return line
        return filename.replace('.pdf', '').replace('.docx', '')
    
    def _extract_email(self, text):
        """Extract email address"""
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "Not found"
    
    def _extract_phone(self, text):
        """Extract phone number"""
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "Not found"
    
    def _extract_skills(self, text):
        """Extract technical skills"""
        skills_keywords = [
            "python", "java", "javascript", "typescript", "c++", "c#", "php", "ruby", "go",
            "html", "css", "react", "angular", "vue", "node.js", "express", "django", "flask",
            "sql", "mysql", "postgresql", "mongodb", "redis", "elasticsearch",
            "aws", "azure", "gcp", "docker", "kubernetes", "jenkins", "git",
            "machine learning", "data analysis", "data science", "ai", "tensorflow", "pytorch",
            "excel", "tableau", "power bi", "project management", "agile", "scrum"
        ]
        
        found_skills = []
        text_lower = text.lower()
        
        for skill in skills_keywords:
            if re.search(r'\b' + re.escape(skill) + r'\b', text_lower):
                found_skills.append(skill)
        
        return found_skills
    
    def _extract_education(self, text):
        """Extract education information"""
        education = []
        text_lower = text.lower()
        
        # Look for degrees
        degree_patterns = [
            r'bachelor.*?(?:in|of)?\s*([a-zA-Z\s]+)',
            r'master.*?(?:in|of)?\s*([a-zA-Z\s]+)',
            r'phd.*?(?:in|of)?\s*([a-zA-Z\s]+)',
        ]
        
        for pattern in degree_patterns:
            matches = re.findall(pattern, text_lower)
            for match in matches:
                education.append(match.strip()[:50])
        
        return education if education else ["Education background"]
    
    def _extract_experience(self, text):
        """Extract years of experience"""
        # Look for explicit years
        years_patterns = [
            r'(\d+)\+?\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience|exp)',
            r'(\d{4})\s*[-–—to]\s*(\d{4}|\bpresent\b)',
        ]
        
        max_years = 0
        for pattern in years_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches:
                if isinstance(match, tuple):
                    if match[1].isdigit():
                        years = int(match[1]) - int(match[0])
                    else:
                        years = 2024 - int(match[0])
                else:
                    years = int(match)
                max_years = max(max_years, years)
        
        return max_years
    
    def _create_summary(self, text):
        """Create a brief summary of the candidate"""
        # Extract first few meaningful lines
        lines = text.split('\n')
        summary_lines = []
        
        for line in lines:
            line = line.strip()
            if line and len(line) > 20 and len(summary_lines) < 3:
                summary_lines.append(line)
        
        return ' '.join(summary_lines)[:200] + "..." if summary_lines else "No summary available"
    
    def chat(self, user_input):
        """Main chat interface with the AI agent"""
        user_input = user_input.strip()
        
        # Add to conversation history
        self.conversation_history.append({"role": "user", "content": user_input})
        
        # Process the input
        response = self._process_user_input(user_input)
        
        # Add response to history
        self.conversation_history.append({"role": "assistant", "content": response})
        
        return response
    
    def _process_user_input(self, user_input):
        """Process user input and generate intelligent response"""
        user_input_lower = user_input.lower()

        # Always try to be helpful and responsive
        # First check if resumes are loaded for most queries
        needs_resumes = any(word in user_input_lower for word in [
            'find', 'search', 'who', 'rank', 'best', 'top', 'compare', 'vs', 'versus',
            'skills', 'experience', 'education', 'list', 'show', 'all', 'candidates',
            'resumes', 'people', 'applicants', 'developers', 'engineers', 'analysts'
        ])

        if needs_resumes and not self.resumes_data:
            return "🤖 I'd love to help! But first I need to analyze the resumes. Let me load them for you...\n\n" + self.load_resumes()

        # Handle specific commands
        if any(word in user_input_lower for word in ['load', 'analyze', 'process']):
            return self.load_resumes()

        elif any(word in user_input_lower for word in ['help', 'what can you do', 'commands']):
            return self._get_help_message()

        # For all other queries, try to understand and respond intelligently
        else:
            return self._generate_adaptive_response(user_input)
    
    def _handle_search_query(self, query):
        """Handle search queries"""
        if not self.resumes_data:
            return "❌ No resumes loaded. Please ask me to 'load resumes' first."
        
        # Extract search terms
        query_lower = query.lower()
        
        # Look for specific skills or keywords
        matches = []
        for filename, resume in self.resumes_data.items():
            score = 0
            reasons = []
            
            # Check skills
            for skill in resume['skills']:
                if skill.lower() in query_lower:
                    score += 2
                    reasons.append(f"has {skill}")
            
            # Check text content
            if any(word in resume['raw_text'].lower() for word in query_lower.split()):
                score += 1
                reasons.append("matches query terms")
            
            if score > 0:
                matches.append((resume, score, reasons))
        
        if not matches:
            return f"❌ No candidates found matching '{query}'"
        
        # Sort by score
        matches.sort(key=lambda x: x[1], reverse=True)
        
        response = f"🔍 Found {len(matches)} candidates matching '{query}':\n\n"
        
        for i, (resume, score, reasons) in enumerate(matches[:5], 1):
            response += f"{i}. **{resume['name']}**\n"
            response += f"   📧 {resume['email']}\n"
            response += f"   🛠️ Skills: {', '.join(resume['skills'][:5])}\n"
            response += f"   ✅ Match reasons: {', '.join(reasons)}\n\n"
        
        return response
    
    def _handle_ranking_query(self, query):
        """Handle ranking queries"""
        if not self.resumes_data:
            return "❌ No resumes loaded. Please ask me to 'load resumes' first."
        
        # Simple ranking by number of skills
        ranked = sorted(self.resumes_data.items(), 
                       key=lambda x: len(x[1]['skills']), reverse=True)
        
        response = f"🏆 Top candidates ranked by skills:\n\n"
        
        for i, (filename, resume) in enumerate(ranked[:5], 1):
            emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "📋"
            response += f"{emoji} {i}. **{resume['name']}**\n"
            response += f"   📊 Skills count: {len(resume['skills'])}\n"
            response += f"   💼 Experience: {resume['experience']} years\n"
            response += f"   🛠️ Top skills: {', '.join(resume['skills'][:4])}\n\n"
        
        return response
    
    def _handle_comparison_query(self, query):
        """Handle comparison queries"""
        if not self.resumes_data:
            return "❌ No resumes loaded. Please ask me to 'load resumes' first."
        
        # For now, compare top 2 candidates
        candidates = list(self.resumes_data.values())[:2]
        
        if len(candidates) < 2:
            return "❌ Need at least 2 candidates to compare."
        
        response = f"⚖️ Comparison of top 2 candidates:\n\n"
        
        for i, candidate in enumerate(candidates, 1):
            response += f"**Candidate {i}: {candidate['name']}**\n"
            response += f"📧 Email: {candidate['email']}\n"
            response += f"💼 Experience: {candidate['experience']} years\n"
            response += f"🛠️ Skills ({len(candidate['skills'])}): {', '.join(candidate['skills'][:6])}\n"
            response += f"🎓 Education: {', '.join(candidate['education'][:2])}\n\n"
        
        return response
    
    def _handle_attribute_query(self, query):
        """Handle queries about specific attributes"""
        if not self.resumes_data:
            return "❌ No resumes loaded. Please ask me to 'load resumes' first."
        
        query_lower = query.lower()
        
        if 'skills' in query_lower:
            # Show most common skills
            all_skills = []
            for resume in self.resumes_data.values():
                all_skills.extend(resume['skills'])
            
            skill_counts = {}
            for skill in all_skills:
                skill_counts[skill] = skill_counts.get(skill, 0) + 1
            
            top_skills = sorted(skill_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
            response = "🛠️ Most common skills across all candidates:\n\n"
            for skill, count in top_skills:
                response += f"• {skill}: {count} candidates\n"
            
            return response
        
        elif 'experience' in query_lower:
            experiences = [resume['experience'] for resume in self.resumes_data.values()]
            avg_exp = sum(experiences) / len(experiences) if experiences else 0
            max_exp = max(experiences) if experiences else 0
            
            return f"💼 Experience overview:\n• Average: {avg_exp:.1f} years\n• Maximum: {max_exp} years\n• Range: {min(experiences) if experiences else 0}-{max_exp} years"
        
        return "❓ I can help with skills, experience, or education queries."
    
    def _list_candidates(self):
        """List all candidates"""
        if not self.resumes_data:
            return "❌ No resumes loaded. Please ask me to 'load resumes' first."
        
        response = f"👥 All {len(self.resumes_data)} candidates:\n\n"
        
        for i, (filename, resume) in enumerate(self.resumes_data.items(), 1):
            response += f"{i}. **{resume['name']}**\n"
            response += f"   📧 {resume['email']}\n"
            response += f"   💼 {resume['experience']} years experience\n"
            response += f"   🛠️ {len(resume['skills'])} skills\n\n"
        
        return response
    
    def _get_help_message(self):
        """Get help message"""
        return """🤖 **AI Resume Agent Help**

I can help you analyze resumes intelligently! Here's what I can do:

**📁 Loading & Analysis:**
• "Load resumes" - Analyze all resumes in the folder
• "Process the resumes" - Same as above

**🔍 Search & Find:**
• "Find candidates with Python skills"
• "Who has machine learning experience?"
• "Search for AWS experts"

**🏆 Ranking & Comparison:**
• "Rank the best candidates"
• "Show me the top candidates"
• "Compare the candidates"

**📊 Analysis:**
• "What skills are most common?"
• "Show me experience levels"
• "List all candidates"

**💬 Natural Conversation:**
Just ask me anything about the candidates in natural language!

Ready to help you find the perfect candidate! 🎯"""
    
    def _generate_adaptive_response(self, user_input):
        """Generate adaptive response to any question"""
        user_input_lower = user_input.lower()

        # Analyze the question and determine intent
        if any(word in user_input_lower for word in ['find', 'search', 'who', 'show me']):
            return self._handle_search_query(user_input)

        elif any(word in user_input_lower for word in ['rank', 'best', 'top', 'good', 'better']):
            return self._handle_ranking_query(user_input)

        elif any(word in user_input_lower for word in ['compare', 'vs', 'versus', 'difference']):
            return self._handle_comparison_query(user_input)

        elif any(word in user_input_lower for word in ['skills', 'experience', 'education', 'background']):
            return self._handle_attribute_query(user_input)

        elif any(word in user_input_lower for word in ['list', 'show', 'all', 'everyone']):
            return self._list_candidates()

        elif any(word in user_input_lower for word in ['how many', 'count', 'number']):
            return self._handle_count_query(user_input)

        elif any(word in user_input_lower for word in ['tell me', 'what', 'explain', 'describe']):
            return self._handle_information_query(user_input)

        elif any(word in user_input_lower for word in ['recommend', 'suggest', 'advice', 'should']):
            return self._handle_recommendation_query(user_input)

        elif any(word in user_input_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            return self._handle_greeting(user_input)

        elif any(word in user_input_lower for word in ['thank', 'thanks', 'appreciate']):
            return "🤖 You're very welcome! I'm here to help you find the perfect candidates. Is there anything else you'd like to know about the resumes?"

        else:
            # Try to understand and respond to any other question
            return self._generate_contextual_response(user_input)

    def _generate_intelligent_response(self, user_input):
        """Generate intelligent response using AI reasoning"""
        if self.openai_available:
            return self._generate_openai_response(user_input)
        else:
            return self._generate_rule_based_response(user_input)
    
    def _generate_openai_response(self, user_input):
        """Generate response using OpenAI"""
        try:
            # Create context about available resumes
            context = f"I have {len(self.resumes_data)} resumes loaded. "
            if self.resumes_data:
                context += "Candidates include: " + ", ".join([r['name'] for r in self.resumes_data.values()][:5])
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"You are an AI resume analysis agent. {context}"},
                    {"role": "user", "content": user_input}
                ],
                max_tokens=300
            )
            
            return response.choices[0].message.content
        except:
            return self._generate_rule_based_response(user_input)
    
    def _generate_rule_based_response(self, user_input):
        """Generate rule-based response"""
        responses = [
            "🤖 I understand you're asking about resume analysis. Could you be more specific?",
            "💭 That's an interesting question! Try asking me to 'find candidates with [skill]' or 'rank the candidates'.",
            "🎯 I'm here to help with resume analysis! Ask me about skills, experience, or specific candidates.",
            "📋 I can help you find the right candidates. Try 'show me the best candidates' or 'find Python developers'."
        ]
        
        import random
        return random.choice(responses)

    def _handle_count_query(self, user_input):
        """Handle counting questions"""
        if not self.resumes_data:
            return "🤖 I don't have any resumes loaded yet. Let me load them first!\n\n" + self.load_resumes()

        user_input_lower = user_input.lower()

        if 'resumes' in user_input_lower or 'candidates' in user_input_lower:
            return f"📊 I have **{len(self.resumes_data)}** resumes loaded and ready for analysis!"

        elif any(skill in user_input_lower for skill in ['python', 'java', 'javascript', 'sql']):
            # Count candidates with specific skills
            skill_counts = {}
            for resume in self.resumes_data.values():
                for skill in resume['skills']:
                    if skill.lower() in user_input_lower:
                        skill_counts[skill] = skill_counts.get(skill, 0) + 1

            if skill_counts:
                response = "📊 Skill counts:\n"
                for skill, count in skill_counts.items():
                    response += f"• {skill}: {count} candidates\n"
                return response
            else:
                return f"🔍 I didn't find any candidates with the skills mentioned in your question."

        else:
            return f"📊 I have {len(self.resumes_data)} total candidates. What specifically would you like me to count?"

    def _handle_information_query(self, user_input):
        """Handle information/explanation questions"""
        if not self.resumes_data:
            return "🤖 I'd be happy to explain! But first let me load the resumes so I can give you specific information.\n\n" + self.load_resumes()

        user_input_lower = user_input.lower()

        if any(word in user_input_lower for word in ['about', 'summary', 'overview']):
            return f"""📋 **Resume Database Overview:**

🔢 **Total Candidates:** {len(self.resumes_data)}
📊 **Average Experience:** {sum(r['experience'] for r in self.resumes_data.values()) / len(self.resumes_data):.1f} years
🛠️ **Total Unique Skills:** {len(set(skill for r in self.resumes_data.values() for skill in r['skills']))}

**Top Candidates:**
{chr(10).join([f"• {list(self.resumes_data.values())[i]['name']}" for i in range(min(3, len(self.resumes_data)))])}

What specific information would you like to know about these candidates?"""

        else:
            return "🤖 I can explain anything about the candidates! Try asking 'tell me about the candidates' or 'what skills do they have?'"

    def _handle_recommendation_query(self, user_input):
        """Handle recommendation questions"""
        if not self.resumes_data:
            return "🤖 I'd love to make recommendations! Let me first analyze the resumes.\n\n" + self.load_resumes()

        user_input_lower = user_input.lower()

        # Get top candidates
        ranked = sorted(self.resumes_data.items(),
                       key=lambda x: len(x[1]['skills']) + x[1]['experience'], reverse=True)

        if any(word in user_input_lower for word in ['hire', 'choose', 'select', 'pick']):
            top_candidate = ranked[0][1]
            return f"""💡 **My Recommendation:**

🥇 **I recommend {top_candidate['name']}**

**Why:**
• 💼 {top_candidate['experience']} years of experience
• 🛠️ {len(top_candidate['skills'])} relevant skills including: {', '.join(top_candidate['skills'][:5])}
• 📧 Contact: {top_candidate['email']}

**Runner-ups:**
{chr(10).join([f"• {ranked[i][1]['name']} ({ranked[i][1]['experience']} years exp)" for i in range(1, min(3, len(ranked)))])}

Would you like me to explain my reasoning or compare these candidates?"""

        else:
            return "💡 I can recommend candidates based on your specific needs! What type of role or skills are you looking for?"

    def _handle_greeting(self, user_input):
        """Handle greetings"""
        greetings = [
            "👋 Hello! I'm your AI Resume Analysis Agent. I'm here to help you find the perfect candidates!",
            "🤖 Hi there! Ready to dive into some resume analysis? I can help you find, rank, and compare candidates.",
            "👋 Hey! I'm excited to help you with resume analysis. What would you like to know about the candidates?",
            "🤖 Hello! I'm your intelligent resume assistant. I can analyze, search, and reason about candidates for you."
        ]

        import random
        greeting = random.choice(greetings)

        if not self.resumes_data:
            greeting += "\n\n💡 I can start by loading and analyzing your resumes. Just say 'load resumes' or ask me anything!"
        else:
            greeting += f"\n\n📊 I have {len(self.resumes_data)} resumes ready for analysis. What would you like to know?"

        return greeting

    def _generate_contextual_response(self, user_input):
        """Generate contextual response for any other question"""
        user_input_lower = user_input.lower()

        # Try to extract key terms and respond contextually
        key_terms = []

        # Technical skills
        tech_skills = ['python', 'java', 'javascript', 'sql', 'react', 'aws', 'machine learning', 'data']
        for skill in tech_skills:
            if skill in user_input_lower:
                key_terms.append(skill)

        # Experience levels
        if any(word in user_input_lower for word in ['senior', 'junior', 'experienced', 'entry']):
            key_terms.append('experience_level')

        # Job roles
        roles = ['developer', 'engineer', 'analyst', 'scientist', 'manager']
        for role in roles:
            if role in user_input_lower:
                key_terms.append(role)

        if key_terms:
            if not self.resumes_data:
                return f"🤖 I understand you're asking about {', '.join(key_terms)}! Let me load the resumes first so I can give you specific information.\n\n" + self.load_resumes()
            else:
                return f"🤖 Great question about {', '.join(key_terms)}! Let me search through the {len(self.resumes_data)} resumes I have loaded...\n\n" + self._handle_search_query(user_input)

        # Default adaptive response
        responses = [
            f"🤖 That's an interesting question! I have {len(self.resumes_data) if self.resumes_data else 'no'} resumes loaded. Could you be more specific about what you'd like to know?",
            "💭 I want to help you with that! Try asking me to 'find candidates with [skill]' or 'show me the best candidates'.",
            "🎯 I'm here to help with resume analysis! You can ask me about skills, experience, rankings, or anything else about the candidates.",
            "🤖 I understand you have a question about the resumes. Feel free to ask me anything - I'll do my best to help!"
        ]

        import random
        return random.choice(responses)

def main():
    """Main conversation loop"""
    agent = AIResumeAgent()
    
    print("\n" + "="*60)
    print("🤖 **AI Resume Analysis Agent** 🤖")
    print("="*60)
    print("💬 Chat with me about resumes! I can analyze, search, rank, and reason about candidates.")
    print("💡 Type 'help' to see what I can do, or 'quit' to exit.")
    print("="*60)
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("🤖 Agent: Goodbye! Happy hiring! 👋")
                break
            
            if not user_input:
                continue
            
            print("🤖 Agent:", end=" ")
            response = agent.chat(user_input)
            print(response)
            
        except KeyboardInterrupt:
            print("\n🤖 Agent: Goodbye! 👋")
            break
        except Exception as e:
            print(f"🤖 Agent: Sorry, I encountered an error: {e}")

if __name__ == "__main__":
    main()
