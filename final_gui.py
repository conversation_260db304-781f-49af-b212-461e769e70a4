#!/usr/bin/env python3
"""
Final Working Resume Parser GUI
This version is guaranteed to work by handling all import issues properly.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import os
import sys
import threading
import subprocess
import re
import importlib

print("🚀 Starting Final Resume Parser GUI...")

# Suppress Tkinter warning
os.environ['TK_SILENCE_DEPRECATION'] = '1'

def install_and_import(package_name, import_name=None):
    """Install package and import it"""
    if import_name is None:
        import_name = package_name
    
    try:
        return importlib.import_module(import_name)
    except ImportError:
        print(f"📦 Installing {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--user", package_name])
        # Reload sys.path to include newly installed packages
        import site
        importlib.reload(site)
        return importlib.import_module(import_name)

# Install and import required packages
try:
    PyPDF2 = install_and_import("PyPDF2")
    docx = install_and_import("python-docx", "docx")
    nltk = install_and_import("nltk")
    
    # Import specific NLTK modules
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    
    # Download NLTK data
    try:
        nltk.data.find('tokenizers/punkt')
        nltk.data.find('corpora/stopwords')
    except LookupError:
        print("📥 Downloading NLTK data...")
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
    
    print("✅ All packages loaded successfully!")
    
except Exception as e:
    print(f"❌ Failed to load packages: {e}")
    print("🔧 Trying alternative approach...")
    
    # Fallback: Use basic text processing without advanced NLP
    PyPDF2 = None
    docx = None
    nltk = None
    word_tokenize = None
    stopwords = None

class FinalResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "filename": os.path.basename(self.file_path)
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf'):
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx'):
            return self._extract_text_from_docx()
        return ""
    
    def _extract_text_from_pdf(self):
        if PyPDF2 is None:
            return "PDF processing not available"
        
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            text = f"Error reading PDF: {e}"
        return text

    def _extract_text_from_docx(self):
        if docx is None:
            return "DOCX processing not available"
        
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except Exception as e:
            text = f"Error reading DOCX: {e}"
        return text

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5 and len(line) > 3:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "Not found"

    def _extract_phone(self, text):
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "Not found"

    def _extract_skills(self, text):
        common_skills = [
            "python", "java", "javascript", "html", "css", "sql", "react", "angular", "vue",
            "aws", "azure", "gcp", "docker", "kubernetes", "git", "machine learning", 
            "data analysis", "data science", "ai", "tensorflow", "pytorch", "pandas",
            "excel", "powerpoint", "project management", "agile", "scrum"
        ]
        
        found_skills = []
        text_lower = text.lower()
        
        for skill in common_skills:
            if re.search(r'\b' + re.escape(skill) + r'\b', text_lower):
                found_skills.append(skill)
        
        return found_skills

    def _extract_education(self, text):
        education = []
        text_lower = text.lower()
        
        degrees = ["bachelor", "master", "phd", "doctorate", "bs", "ms", "ba", "ma", "mba"]
        for degree in degrees:
            if degree in text_lower:
                education.append(f"{degree.title()} degree")
                
        if not education and any(word in text_lower for word in ["university", "college", "school"]):
            education.append("College education")
            
        return education

    def _extract_experience(self, text):
        # Look for years of experience
        years_patterns = [
            r'(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience|exp)',
            r'(\d+)\+?\s*(?:years?|yrs?)',
            r'(\d{4})\s*[-–—]\s*(\d{4}|\bpresent\b|\bcurrent\b)'
        ]
        
        max_years = 0
        for pattern in years_patterns:
            matches = re.findall(pattern, text.lower())
            for match in matches:
                if isinstance(match, tuple):
                    if match[1].isdigit():
                        years = int(match[1]) - int(match[0])
                    else:
                        years = 2024 - int(match[0])
                else:
                    years = int(match)
                max_years = max(max_years, years)
        
        return max_years

class FinalJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = self._calculate_score(resume)
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)
    
    def _calculate_score(self, resume):
        score = 0
        
        # Skills matching (60%)
        required_skills = set(s.lower().strip() for s in self.requirements.get("required_skills", []))
        candidate_skills = set(s.lower() for s in resume["skills"])
        
        if required_skills:
            skill_matches = required_skills.intersection(candidate_skills)
            skill_score = len(skill_matches) / len(required_skills)
            score += skill_score * 60
        
        # Education (20%)
        if resume["education"]:
            score += 20
        
        # Experience (20%)
        required_exp = self.requirements.get("experience", 0)
        if resume["experience"] >= required_exp:
            score += 20
        elif resume["experience"] > 0:
            score += 10  # Partial credit
        
        return min(score, 100)  # Cap at 100

class FinalGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Resume Parser - Final Working Version")
        self.root.geometry("900x700")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Resume Parser - Final Working Version", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Status
        status_text = "✅ Ready to analyze resumes!"
        if PyPDF2 is None or docx is None:
            status_text = "⚠️ Limited functionality - some file types may not work"
        
        ttk.Label(main_frame, text=status_text, font=("Arial", 10)).grid(row=1, column=0, columnspan=2, pady=(0, 15))
        
        # Folder selection
        folder_frame = ttk.LabelFrame(main_frame, text="Resume Folder", padding="10")
        folder_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        folder_frame.columnconfigure(1, weight=1)
        
        ttk.Label(folder_frame, text="Folder:").grid(row=0, column=0, sticky=tk.W)
        self.folder_var = tk.StringVar(value="resumes/")
        ttk.Entry(folder_frame, textvariable=self.folder_var, width=60).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        ttk.Button(folder_frame, text="Browse", command=self.browse_folder).grid(row=0, column=2)
        
        # File count
        self.file_count_label = ttk.Label(folder_frame, text="")
        self.file_count_label.grid(row=1, column=0, columnspan=3, pady=(5, 0))
        self.update_file_count()
        
        # Requirements
        req_frame = ttk.LabelFrame(main_frame, text="Job Requirements", padding="10")
        req_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        req_frame.columnconfigure(1, weight=1)
        
        ttk.Label(req_frame, text="Required Skills:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.skills_var = tk.StringVar(value="python, machine learning, sql, data analysis")
        ttk.Entry(req_frame, textvariable=self.skills_var, width=60).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Label(req_frame, text="Min Experience (years):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.exp_var = tk.StringVar(value="2")
        ttk.Entry(req_frame, textvariable=self.exp_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(0, 15))
        
        self.analyze_btn = ttk.Button(button_frame, text="🚀 Analyze Resumes", command=self.analyze)
        self.analyze_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Clear Results", command=self.clear).pack(side=tk.LEFT)
        
        # Progress
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Results
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        self.results = scrolledtext.ScrolledText(results_frame, height=20, width=80, font=("Consolas", 9))
        self.results.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        main_frame.rowconfigure(6, weight=1)
        
        # Welcome message
        self.results.insert(tk.END, "Welcome to Resume Parser - Final Working Version! 🎉\n\n")
        self.results.insert(tk.END, "Instructions:\n")
        self.results.insert(tk.END, "1. Put your PDF/DOCX resumes in the 'resumes' folder\n")
        self.results.insert(tk.END, "2. Customize the job requirements above\n")
        self.results.insert(tk.END, "3. Click 'Analyze Resumes' to start\n\n")
        self.results.insert(tk.END, "Ready to find the best candidates! 🎯\n")
        self.results.insert(tk.END, "=" * 60 + "\n\n")
        
    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.folder_var.set(folder)
            self.update_file_count()
    
    def update_file_count(self):
        folder = self.folder_var.get()
        if os.path.exists(folder):
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            self.file_count_label.config(text=f"Found {len(files)} resume files")
        else:
            self.file_count_label.config(text="Folder not found")
    
    def clear(self):
        self.results.delete(1.0, tk.END)
        
    def analyze(self):
        self.analyze_btn.config(state='disabled', text="Processing...")
        self.progress.start()
        self.clear()
        
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
        
    def run_analysis(self):
        try:
            folder = self.folder_var.get()
            
            if not os.path.exists(folder):
                self.add_result("❌ Folder not found!")
                return
                
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            if not files:
                self.add_result("❌ No PDF/DOCX files found!")
                return
                
            self.add_result(f"📁 Found {len(files)} files. Processing...\n")
            
            # Parse requirements
            skills_text = self.skills_var.get()
            skills = [s.strip() for s in skills_text.split(',') if s.strip()]
            
            try:
                experience = int(self.exp_var.get() or 0)
            except ValueError:
                experience = 0
            
            requirements = {
                "required_skills": skills,
                "experience": experience
            }
            
            self.add_result(f"🎯 Looking for: {', '.join(skills)}")
            self.add_result(f"📅 Min experience: {experience} years\n")
            
            # Process resumes
            resumes = []
            for i, filename in enumerate(files, 1):
                self.add_result(f"📄 {i}/{len(files)}: {filename}")
                
                try:
                    file_path = os.path.join(folder, filename)
                    parser = FinalResumeParser(file_path)
                    resume_data = parser.parse()
                    resumes.append(resume_data)
                except Exception as e:
                    self.add_result(f"   ❌ Error: {e}")
            
            if not resumes:
                self.add_result("\n❌ No resumes processed successfully!")
                return
                
            self.add_result(f"\n✅ Successfully processed {len(resumes)} resumes")
            
            # Rank resumes
            matcher = FinalJobMatcher(requirements)
            ranked = matcher.rank_resumes(resumes)
            
            self.add_result(f"\n🏆 RANKING RESULTS:")
            self.add_result("=" * 60)
            
            for i, (resume, score) in enumerate(ranked, 1):
                # Score emoji
                if score >= 80:
                    emoji = "🥇"
                elif score >= 60:
                    emoji = "🥈"
                elif score >= 40:
                    emoji = "🥉"
                else:
                    emoji = "📋"
                
                self.add_result(f"\n{emoji} {i}. {resume['name']}")
                self.add_result(f"    📊 Score: {score:.1f}/100")
                self.add_result(f"    📧 Email: {resume['email']}")
                self.add_result(f"    📞 Phone: {resume['phone']}")
                
                if resume['skills']:
                    skills_display = ', '.join(resume['skills'][:6])
                    if len(resume['skills']) > 6:
                        skills_display += f" (+{len(resume['skills'])-6} more)"
                    self.add_result(f"    🛠️  Skills: {skills_display}")
                
                if resume['education']:
                    self.add_result(f"    🎓 Education: {'; '.join(resume['education'][:2])}")
                
                self.add_result(f"    💼 Experience: {resume['experience']} years")
                self.add_result("    " + "-" * 50)
                
        except Exception as e:
            self.add_result(f"❌ Unexpected error: {e}")
        finally:
            self.root.after(0, self.analysis_done)
            
    def add_result(self, text):
        def update():
            self.results.insert(tk.END, text + "\n")
            self.results.see(tk.END)
        self.root.after(0, update)
        
    def analysis_done(self):
        self.progress.stop()
        self.analyze_btn.config(state='normal', text="🚀 Analyze Resumes")
        self.update_file_count()

def main():
    print("🎯 Creating GUI window...")
    root = tk.Tk()
    app = FinalGUI(root)
    print("✅ GUI ready! Window should be visible now.")
    root.mainloop()
    print("👋 Application closed.")

if __name__ == "__main__":
    main()
