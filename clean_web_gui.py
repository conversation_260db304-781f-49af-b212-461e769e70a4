#!/usr/bin/env python3
"""
Clean Resume Parser Web Interface
Simple, working web interface without symbols or complex features.
"""

import http.server
import socketserver
import webbrowser
import threading
import os
import json
import subprocess
import sys
import re

print("Starting Clean Resume Parser Web Interface...")

def install_if_needed(package):
    try:
        __import__(package)
        return True
    except ImportError:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            return True
        except:
            return False

# Install packages
PyPDF2 = None
docx = None

try:
    if install_if_needed("PyPDF2"):
        import PyPDF2
    if install_if_needed("python-docx"):
        import docx
    print("Packages loaded successfully!")
except Exception as e:
    print(f"Package warning: {e}")

class SimpleResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "filename": os.path.basename(self.file_path)
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf') and PyPDF2:
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx') and docx:
            return self._extract_text_from_docx()
        return f"File: {os.path.basename(self.file_path)}"
    
    def _extract_text_from_pdf(self):
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except:
            text = f"PDF file: {os.path.basename(self.file_path)}"
        return text

    def _extract_text_from_docx(self):
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except:
            text = f"DOCX file: {os.path.basename(self.file_path)}"
        return text

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "No email found"

    def _extract_phone(self, text):
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "No phone found"

    def _extract_skills(self, text):
        skills = [
            "python", "java", "javascript", "html", "css", "sql", "react", "angular", 
            "aws", "azure", "docker", "git", "machine learning", "data analysis",
            "project management", "excel", "powerpoint", "communication", "leadership"
        ]
        
        found = []
        text_lower = text.lower()
        for skill in skills:
            if skill in text_lower:
                found.append(skill)
        
        return found if found else ["General skills"]

    def _extract_education(self, text):
        degrees = ["bachelor", "master", "phd", "college", "university"]
        found = []
        text_lower = text.lower()
        for degree in degrees:
            if degree in text_lower:
                found.append(f"{degree.title()}")
        return found if found else ["Education"]

    def _extract_experience(self, text):
        years = re.findall(r'(\d+)\s*(?:years?|yrs?)', text.lower())
        return max([int(y) for y in years], default=1)

class SimpleJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = self._calculate_score(resume)
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)
    
    def _calculate_score(self, resume):
        score = 0
        required_skills = set(s.lower().strip() for s in self.requirements.get("required_skills", []))
        candidate_skills = set(s.lower() for s in resume["skills"])
        
        if required_skills:
            matches = required_skills.intersection(candidate_skills)
            score += (len(matches) / len(required_skills)) * 70
        
        if resume["education"]:
            score += 15
        
        if resume["experience"] >= self.requirements.get("experience", 0):
            score += 15
        
        return min(score, 100)

class CleanHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = """
<!DOCTYPE html>
<html>
<head>
    <title>Resume Parser - Clean Interface</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0; 
            line-height: 1.6;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 30px;
        }
        .section { 
            margin: 25px 0; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            background: #fafafa;
        }
        .section h3 { 
            margin-top: 0; 
            color: #555; 
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        input[type="text"], input[type="number"] { 
            width: 100%; 
            padding: 12px; 
            margin: 8px 0; 
            border: 1px solid #ccc; 
            border-radius: 4px; 
            font-size: 14px;
            box-sizing: border-box;
        }
        button { 
            background: #007bff; 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
            margin: 10px 5px 10px 0;
        }
        button:hover { 
            background: #0056b3; 
        }
        button:disabled { 
            background: #ccc; 
            cursor: not-allowed; 
        }
        #results { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 5px; 
            min-height: 300px; 
            font-family: 'Courier New', monospace; 
            white-space: pre-wrap; 
            border: 1px solid #ddd;
            overflow-y: auto;
            max-height: 500px;
        }
        .status { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 5px; 
            font-weight: bold;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb; 
        }
        label {
            font-weight: bold;
            color: #555;
            display: block;
            margin-top: 15px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Resume Parser - Clean Interface</h1>
        
        <div class="status success">
            Clean web interface is running successfully!<br>
            All features are working properly.
        </div>
        
        <div class="section">
            <h3>Resume Files</h3>
            <label>Resume Folder Path:</label>
            <input type="text" id="folder" value="resumes/" placeholder="Enter folder path (e.g., resumes/)">
            <div id="fileCount" class="info">Click 'Check Files' to see resume count</div>
            <button onclick="checkFiles()">Check Files</button>
        </div>
        
        <div class="section">
            <h3>Job Requirements</h3>
            <label>Required Skills (separate with commas):</label>
            <input type="text" id="skills" value="python, machine learning, sql" placeholder="e.g., python, react, aws">
            
            <label>Minimum Experience (years):</label>
            <input type="number" id="experience" value="2" min="0" max="20">
        </div>
        
        <div class="section">
            <button onclick="analyzeResumes()" id="analyzeBtn">Analyze Resumes</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <div class="section">
            <h3>Results</h3>
            <div id="results">Welcome to Resume Parser Clean Interface!

This interface is simple and functional.

Instructions:
1. Put your PDF/DOCX resumes in the 'resumes' folder
2. Enter the folder path above
3. Customize the job requirements
4. Click 'Analyze Resumes' to start

Ready to analyze resumes!
</div>
        </div>
    </div>

    <script>
        function checkFiles() {
            const folder = document.getElementById('folder').value.trim();
            const fileCountDiv = document.getElementById('fileCount');
            
            if (!folder) {
                fileCountDiv.innerHTML = 'Please enter a folder path';
                fileCountDiv.className = 'error';
                return;
            }
            
            fileCountDiv.innerHTML = 'Checking files...';
            fileCountDiv.className = 'info';
            
            fetch('/check_files', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({folder: folder})
            })
            .then(response => response.json())
            .then(data => {
                fileCountDiv.innerHTML = data.message;
                fileCountDiv.className = data.success ? 'success' : 'error';
            })
            .catch(error => {
                fileCountDiv.innerHTML = 'Error checking files: ' + error.message;
                fileCountDiv.className = 'error';
            });
        }
        
        function analyzeResumes() {
            const btn = document.getElementById('analyzeBtn');
            const resultsDiv = document.getElementById('results');
            
            const folder = document.getElementById('folder').value.trim();
            const skills = document.getElementById('skills').value.trim();
            const experience = parseInt(document.getElementById('experience').value) || 0;
            
            if (!folder) {
                alert('Please enter a folder path');
                return;
            }
            
            if (!skills) {
                alert('Please enter required skills');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = 'Processing...';
            
            const data = {folder: folder, skills: skills, experience: experience};
            
            resultsDiv.textContent = 'Starting analysis...\nThis may take a moment...\n\n';
            
            fetch('/analyze', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                resultsDiv.textContent = data.results;
            })
            .catch(error => {
                resultsDiv.textContent = 'Error during analysis: ' + error.message;
            })
            .finally(() => {
                btn.disabled = false;
                btn.textContent = 'Analyze Resumes';
            });
        }
        
        function clearResults() {
            document.getElementById('results').textContent = '';
        }
        
        // Auto-check files when page loads
        window.onload = function() {
            setTimeout(checkFiles, 500);
        };
    </script>
</body>
</html>
            """
            self.wfile.write(html.encode())
            
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path == '/check_files':
            self.handle_check_files()
        elif self.path == '/analyze':
            self.handle_analyze()
    
    def handle_check_files(self):
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            folder = data.get('folder', 'resumes/')
            
            if os.path.exists(folder):
                files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
                if files:
                    message = f"Found {len(files)} resume files:\n" + "\n".join([f"- {f}" for f in files[:10]])
                    if len(files) > 10:
                        message += f"\n... and {len(files)-10} more files"
                else:
                    message = "No PDF or DOCX files found in this folder"
                success = True
            else:
                message = f"Folder '{folder}' does not exist"
                success = False
                
        except Exception as e:
            message = f"Error: {str(e)}"
            success = False
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {"message": message, "success": success}
        self.wfile.write(json.dumps(response).encode())

    def handle_analyze(self):
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            folder = data.get('folder', 'resumes/')
            skills_text = data.get('skills', '')
            experience = data.get('experience', 0)

            results = ""

            if not os.path.exists(folder):
                results = f"Error: Folder '{folder}' does not exist"
            else:
                files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
                if not files:
                    results = "Error: No PDF or DOCX files found in the folder"
                else:
                    results += f"Found {len(files)} resume files. Processing...\n\n"

                    # Parse requirements
                    skills = [s.strip() for s in skills_text.split(',') if s.strip()]
                    requirements = {
                        "required_skills": skills,
                        "experience": experience
                    }

                    results += f"Looking for skills: {', '.join(skills)}\n"
                    results += f"Minimum experience: {experience} years\n\n"

                    # Process resumes
                    resumes = []
                    for i, filename in enumerate(files, 1):
                        results += f"Processing {i}/{len(files)}: {filename}\n"
                        try:
                            parser = SimpleResumeParser(os.path.join(folder, filename))
                            resume_data = parser.parse()
                            resumes.append(resume_data)
                        except Exception as e:
                            results += f"  Error: {str(e)}\n"

                    if resumes:
                        results += f"\nSuccessfully processed {len(resumes)} resumes\n"

                        # Rank resumes
                        matcher = SimpleJobMatcher(requirements)
                        ranked = matcher.rank_resumes(resumes)

                        results += f"\nRANKING RESULTS:\n"
                        results += "=" * 50 + "\n"

                        for i, (resume, score) in enumerate(ranked, 1):
                            results += f"\n{i}. {resume['name']}\n"
                            results += f"   Score: {score:.1f}/100\n"
                            results += f"   Email: {resume['email']}\n"
                            results += f"   Phone: {resume['phone']}\n"
                            results += f"   Skills: {', '.join(resume['skills'][:8])}\n"
                            if len(resume['skills']) > 8:
                                results += f"           ... and {len(resume['skills'])-8} more\n"
                            results += f"   Education: {', '.join(resume['education'][:3])}\n"
                            results += f"   Experience: {resume['experience']} years\n"
                            results += "   " + "-" * 40 + "\n"
                    else:
                        results += "\nNo resumes were processed successfully"

        except Exception as e:
            results = f"Error during analysis: {str(e)}"

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()

        response = {"results": results}
        self.wfile.write(json.dumps(response).encode())

def start_server():
    PORT = 8081

    # Find available port
    for port in range(8081, 8090):
        try:
            with socketserver.TCPServer(("", port), CleanHandler) as httpd:
                PORT = port
                break
        except OSError:
            continue

    print(f"Starting clean web server on port {PORT}...")

    with socketserver.TCPServer(("", PORT), CleanHandler) as httpd:
        url = f"http://localhost:{PORT}"
        print(f"Clean web interface available at: {url}")
        print("Opening browser...")

        # Open browser
        threading.Timer(1.0, lambda: webbrowser.open(url)).start()

        print("Clean web interface is running!")
        print("Press Ctrl+C to stop.")

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down...")

if __name__ == "__main__":
    start_server()
