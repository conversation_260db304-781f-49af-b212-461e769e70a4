#!/usr/bin/env python3
"""
Simple macOS Resume Parser GUI
Minimal version to fix black screen and buffering issues.
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import os
import sys
import threading
import subprocess
import re

print("Starting Simple macOS Resume Parser...")

# Suppress deprecation warning
os.environ['TK_SILENCE_DEPRECATION'] = '1'

def install_package(package):
    try:
        __import__(package)
        return True
    except ImportError:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            return True
        except:
            return False

# Load packages
PyPDF2 = None
docx = None

try:
    if install_package("PyPDF2"):
        import PyPDF2
    if install_package("python-docx"):
        import docx
    print("Packages loaded!")
except Exception as e:
    print(f"Package warning: {e}")

class SimpleResumeParser:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def parse(self):
        text = self._extract_text()
        return {
            "name": self._extract_name(text),
            "email": self._extract_email(text),
            "phone": self._extract_phone(text),
            "skills": self._extract_skills(text),
            "education": self._extract_education(text),
            "experience": self._extract_experience(text),
            "filename": os.path.basename(self.file_path)
        }
    
    def _extract_text(self):
        if self.file_path.endswith('.pdf') and PyPDF2:
            return self._extract_text_from_pdf()
        elif self.file_path.endswith('.docx') and docx:
            return self._extract_text_from_docx()
        return f"File: {os.path.basename(self.file_path)}"
    
    def _extract_text_from_pdf(self):
        text = ""
        try:
            with open(self.file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
        except:
            text = f"PDF: {os.path.basename(self.file_path)}"
        return text

    def _extract_text_from_docx(self):
        text = ""
        try:
            doc = docx.Document(self.file_path)
            for para in doc.paragraphs:
                text += para.text + "\n"
        except:
            text = f"DOCX: {os.path.basename(self.file_path)}"
        return text

    def _extract_name(self, text):
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line and len(line.split()) <= 5:
                return line
        return os.path.basename(self.file_path).replace('.pdf', '').replace('.docx', '')

    def _extract_email(self, text):
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        return emails[0] if emails else "No email"

    def _extract_phone(self, text):
        phones = re.findall(r'(\+\d{1,3}[-.\s]?)?(\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}', text)
        return ''.join(''.join(tup) for tup in phones[:1]) if phones else "No phone"

    def _extract_skills(self, text):
        skills = ["python", "java", "javascript", "sql", "react", "aws", "machine learning", "data analysis"]
        found = []
        text_lower = text.lower()
        for skill in skills:
            if skill in text_lower:
                found.append(skill)
        return found if found else ["General skills"]

    def _extract_education(self, text):
        degrees = ["bachelor", "master", "phd", "college", "university"]
        found = []
        text_lower = text.lower()
        for degree in degrees:
            if degree in text_lower:
                found.append(degree.title())
        return found if found else ["Education"]

    def _extract_experience(self, text):
        years = re.findall(r'(\d+)\s*(?:years?|yrs?)', text.lower())
        return max([int(y) for y in years], default=1)

class SimpleJobMatcher:
    def __init__(self, requirements):
        self.requirements = requirements
        
    def rank_resumes(self, resumes):
        scored = []
        for resume in resumes:
            score = self._calculate_score(resume)
            scored.append((resume, score))
        return sorted(scored, key=lambda x: x[1], reverse=True)
    
    def _calculate_score(self, resume):
        score = 0
        required_skills = set(s.lower().strip() for s in self.requirements.get("required_skills", []))
        candidate_skills = set(s.lower() for s in resume["skills"])
        
        if required_skills:
            matches = required_skills.intersection(candidate_skills)
            score += (len(matches) / len(required_skills)) * 70
        
        if resume["education"]:
            score += 15
        
        if resume["experience"] >= self.requirements.get("experience", 0):
            score += 15
        
        return min(score, 100)

class SimpleGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Resume Parser - Simple macOS")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # Variables
        self.folder_var = tk.StringVar(value="resumes/")
        self.skills_var = tk.StringVar(value="python, machine learning, sql")
        self.experience_var = tk.StringVar(value="2")
        
        self.create_simple_widgets()
        
    def create_simple_widgets(self):
        """Create simple, reliable widgets"""
        
        # Title
        title_label = tk.Label(self.root, text="Resume Parser - Simple macOS", 
                              font=("Helvetica", 16, "bold"), bg='#f0f0f0')
        title_label.pack(pady=10)
        
        # Status
        status_label = tk.Label(self.root, text="Simple GUI - No buffering issues!", 
                               font=("Helvetica", 12), bg='#f0f0f0', fg='green')
        status_label.pack(pady=5)
        
        # Folder frame
        folder_frame = tk.Frame(self.root, bg='#f0f0f0', relief='ridge', bd=2)
        folder_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(folder_frame, text="Resume Folder", font=("Helvetica", 12, "bold"), 
                bg='#f0f0f0').pack(pady=5)
        
        # Folder input
        folder_input_frame = tk.Frame(folder_frame, bg='#f0f0f0')
        folder_input_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(folder_input_frame, text="Folder:", bg='#f0f0f0').pack(side='left')
        
        self.folder_entry = tk.Entry(folder_input_frame, textvariable=self.folder_var, 
                                    width=50, font=("Helvetica", 11))
        self.folder_entry.pack(side='left', padx=10, fill='x', expand=True)
        
        # Browse button
        browse_btn = tk.Button(folder_input_frame, text="Browse", 
                              command=self.browse_folder, bg='#4CAF50', fg='white',
                              font=("Helvetica", 11), padx=10)
        browse_btn.pack(side='right')
        
        # File count
        self.file_count_label = tk.Label(folder_frame, text="", bg='#f0f0f0', 
                                        font=("Helvetica", 10))
        self.file_count_label.pack(pady=5)
        
        # Check files button
        check_btn = tk.Button(folder_frame, text="Check Files", command=self.check_files,
                             bg='#2196F3', fg='white', font=("Helvetica", 11), padx=10)
        check_btn.pack(pady=5)
        
        # Requirements frame
        req_frame = tk.Frame(self.root, bg='#f0f0f0', relief='ridge', bd=2)
        req_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(req_frame, text="Job Requirements", font=("Helvetica", 12, "bold"), 
                bg='#f0f0f0').pack(pady=5)
        
        # Skills
        skills_frame = tk.Frame(req_frame, bg='#f0f0f0')
        skills_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(skills_frame, text="Skills:", bg='#f0f0f0', width=15, anchor='w').pack(side='left')
        self.skills_entry = tk.Entry(skills_frame, textvariable=self.skills_var, 
                                    font=("Helvetica", 11))
        self.skills_entry.pack(side='right', fill='x', expand=True, padx=10)
        
        # Experience
        exp_frame = tk.Frame(req_frame, bg='#f0f0f0')
        exp_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(exp_frame, text="Experience:", bg='#f0f0f0', width=15, anchor='w').pack(side='left')
        self.exp_entry = tk.Entry(exp_frame, textvariable=self.experience_var, 
                                 width=10, font=("Helvetica", 11))
        self.exp_entry.pack(side='right', padx=10)
        
        # Action buttons
        button_frame = tk.Frame(self.root, bg='#f0f0f0')
        button_frame.pack(pady=20)
        
        self.analyze_btn = tk.Button(button_frame, text="Analyze Resumes", 
                                    command=self.analyze_resumes, bg='#FF5722', fg='white',
                                    font=("Helvetica", 12, "bold"), padx=20, pady=10)
        self.analyze_btn.pack(side='left', padx=10)
        
        clear_btn = tk.Button(button_frame, text="Clear", command=self.clear_results,
                             bg='#9E9E9E', fg='white', font=("Helvetica", 11), padx=15, pady=8)
        clear_btn.pack(side='left', padx=10)
        
        # Results
        results_frame = tk.Frame(self.root, bg='#f0f0f0', relief='ridge', bd=2)
        results_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        tk.Label(results_frame, text="Results", font=("Helvetica", 12, "bold"), 
                bg='#f0f0f0').pack(pady=5)
        
        # Simple text widget instead of scrolledtext
        self.results_text = tk.Text(results_frame, height=15, width=80, 
                                   font=("Courier", 10), wrap='word')
        
        # Add scrollbar manually
        scrollbar = tk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        scrollbar.pack(side="right", fill="y")
        
        # Initial message
        self.results_text.insert(tk.END, "Welcome to Simple Resume Parser!\n\n")
        self.results_text.insert(tk.END, "This version fixes black screen and buffering issues.\n\n")
        self.results_text.insert(tk.END, "Instructions:\n")
        self.results_text.insert(tk.END, "1. Click 'Browse' to select resume folder\n")
        self.results_text.insert(tk.END, "2. Click 'Check Files' to count resumes\n")
        self.results_text.insert(tk.END, "3. Edit skills and experience\n")
        self.results_text.insert(tk.END, "4. Click 'Analyze Resumes'\n\n")
        self.results_text.insert(tk.END, "Ready!\n")
        
        # Auto-check files
        self.root.after(500, self.check_files)
        
    def browse_folder(self):
        """Open file dialog to select folder"""
        try:
            folder = filedialog.askdirectory(
                title="Select Resume Folder",
                initialdir=os.path.expanduser("~")
            )
            
            if folder:
                self.folder_var.set(folder)
                self.check_files()
                self.add_result(f"Selected: {folder}")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open folder browser: {e}")
    
    def check_files(self):
        """Check resume files in folder"""
        try:
            folder = self.folder_var.get()
            
            if os.path.exists(folder):
                files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
                count = len(files)
                self.file_count_label.config(text=f"Found {count} resume files")
                
                if count > 0:
                    self.file_count_label.config(fg="green")
                else:
                    self.file_count_label.config(fg="orange")
            else:
                self.file_count_label.config(text="Folder not found", fg="red")
        except Exception as e:
            self.file_count_label.config(text=f"Error: {e}", fg="red")
    
    def analyze_resumes(self):
        """Start resume analysis"""
        self.analyze_btn.config(state='disabled', text="Processing...")
        self.clear_results()
        
        # Run in thread
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
    
    def run_analysis(self):
        try:
            folder = self.folder_var.get()
            
            if not os.path.exists(folder):
                self.add_result("Error: Folder does not exist!")
                return
            
            files = [f for f in os.listdir(folder) if f.endswith(('.pdf', '.docx'))]
            if not files:
                self.add_result("Error: No resume files found!")
                return
            
            self.add_result(f"Processing {len(files)} files...\n")
            
            # Parse requirements
            skills = [s.strip() for s in self.skills_var.get().split(',') if s.strip()]
            experience = int(self.experience_var.get() or 0)
            
            requirements = {"required_skills": skills, "experience": experience}
            
            self.add_result(f"Skills: {', '.join(skills)}")
            self.add_result(f"Experience: {experience} years\n")
            
            # Process files
            resumes = []
            for i, filename in enumerate(files, 1):
                self.add_result(f"{i}/{len(files)}: {filename}")
                try:
                    parser = SimpleResumeParser(os.path.join(folder, filename))
                    resume_data = parser.parse()
                    resumes.append(resume_data)
                except Exception as e:
                    self.add_result(f"  Error: {e}")
            
            if resumes:
                # Rank resumes
                matcher = SimpleJobMatcher(requirements)
                ranked = matcher.rank_resumes(resumes)
                
                self.add_result(f"\nRESULTS:")
                self.add_result("=" * 50)
                
                for i, (resume, score) in enumerate(ranked, 1):
                    self.add_result(f"\n{i}. {resume['name']}")
                    self.add_result(f"   Score: {score:.1f}/100")
                    self.add_result(f"   Email: {resume['email']}")
                    self.add_result(f"   Phone: {resume['phone']}")
                    self.add_result(f"   Skills: {', '.join(resume['skills'][:5])}")
                    self.add_result(f"   Education: {', '.join(resume['education'][:2])}")
                    self.add_result(f"   Experience: {resume['experience']} years")
                    self.add_result("   " + "-" * 40)
            else:
                self.add_result("\nNo resumes processed successfully")
                
        except Exception as e:
            self.add_result(f"\nError: {e}")
        finally:
            self.root.after(0, self.analysis_complete)
    
    def add_result(self, text):
        """Add text to results"""
        def update():
            self.results_text.insert(tk.END, text + "\n")
            self.results_text.see(tk.END)
            self.root.update_idletasks()
        self.root.after(0, update)
    
    def clear_results(self):
        """Clear results"""
        self.results_text.delete(1.0, tk.END)
    
    def analysis_complete(self):
        """Re-enable analyze button"""
        self.analyze_btn.config(state='normal', text="Analyze Resumes")

def main():
    print("Creating simple GUI...")
    
    root = tk.Tk()
    
    # Force immediate display
    root.update()
    
    app = SimpleGUI(root)
    
    # Force window to front
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    print("Simple GUI ready!")
    root.mainloop()

if __name__ == "__main__":
    main()
